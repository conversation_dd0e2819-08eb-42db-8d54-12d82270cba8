package com.wzsec.modules.service;

import com.wzsec.modules.domain.WebProxyconfig;
import com.wzsec.modules.service.dto.WebProxyconfigDto;
import com.wzsec.modules.service.dto.WebProxyconfigQueryCriteria;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
public interface WebProxyconfigService {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(WebProxyconfigQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<WebProxyconfigDto>
     */
    List<WebProxyconfigDto> queryAll(WebProxyconfigQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return WebProxyconfigDto
     */
    WebProxyconfigDto findById(Integer id);

    /**
     * 创建
     *
     * @param resources /
     * @return WebProxyconfigDto
     */
    WebProxyconfigDto create(WebProxyconfig resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(WebProxyconfig resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Integer[] ids);

    /**
     * 测试数据库连接
     *
     * @param id 数据源id
     * @throws IOException /
     */
    boolean test(Long id);

}