package com.wzsec.modules.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@Entity
@Data
@Table(name = "dwts_web_proxyrecord")
public class WebProxyrecord implements Serializable {

    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 请求IP
     */
    @Column(name = "requestip")
    // @NotBlank
    private String requestip;

    /**
     * 请求时间
     */
    @Column(name = "requesttime")
    private Timestamp requesttime;

    /**
     * 执行状态(成功,失败)
     */
    @Column(name = "executingstate")
    private String executingstate;

    /**
     * web应用名称
     */
    @Column(name = "applicationname")
    // @NotBlank
    private String applicationname;

    /**
     * web应用IP
     */
    @Column(name = "webseverip")
    // @NotBlank
    private String webseverip;

    /**
     * 水印信息
     */
    @Column(name = "webwatermark")
    private String webwatermark;

    /**
     * 备注
     */
    @Column(name = "note")
    private String note;

    /**
     * 插入时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    public void copy(WebProxyrecord source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }
}