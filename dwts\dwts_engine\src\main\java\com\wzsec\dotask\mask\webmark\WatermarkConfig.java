package com.wzsec.dotask.mask.webmark;

import lombok.Data;

import java.io.Serializable;

/**
 * 水印配置类
 * 封装水印处理所需的配置参数
 *
 * <AUTHOR> Team
 * @date 2025/08/06
 */
@Data
public class WatermarkConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 水印文本模板 */
    private String watermarkText;

    /** 是否启用页面水印 */
    private Boolean enablePageWatermark;

    /** 是否启用API水印（暗水印） */
    private Boolean enableApiWatermark;

    /** API路径模式 (逗号分隔) */
    private String apiPathPatterns;

    /** 水印透明度 (0.0-1.0) */
    private Double watermarkOpacity;

    /** 水印宽度 */
    private Integer watermarkWidth;

    /** 水印高度 */
    private Integer watermarkHeight;

    /** 水印颜色 */
    private String watermarkColor;

    /** 水印角度 */
    private Double watermarkAngle;

    /** 是否启用链接重写 */
    private Boolean enableLinkRewrite;

    /** 是否启用API拦截 */
    private Boolean enableApiIntercept;

    /** 暗水印编码强度 */
    private String invisibleEncodingStrength;

    /** 暗水印嵌入密度 */
    private Double invisibleEmbedDensity;

    /** 应用名称 */
    private String applicationName;

    /** 目标主机 */
    private String targetHost;

    /** 目标端口 */
    private Integer targetPort;

    /** 目标协议 */
    private String targetProtocol;

    /**
     * 获取默认配置
     */
    public static WatermarkConfig getDefault() {
        WatermarkConfig config = new WatermarkConfig();
        config.setWatermarkText("DWTS_{IP}_{DATE}");
        config.setEnablePageWatermark(true);
        config.setEnableApiWatermark(true);
        config.setWatermarkOpacity(0.3);
        config.setWatermarkWidth(300);
        config.setWatermarkHeight(150);
        config.setWatermarkColor("#999999");
        config.setWatermarkAngle(-30.0);
        config.setEnableLinkRewrite(false);
        config.setEnableApiIntercept(false);
        config.setInvisibleEncodingStrength("medium");
        config.setInvisibleEmbedDensity(0.3);
        return config;
    }

    /**
     * 检查页面水印是否启用
     */
    public boolean isPageWatermarkEnabled() {
        return Boolean.TRUE.equals(enablePageWatermark);
    }

    /**
     * 检查API水印是否启用
     */
    public boolean isApiWatermarkEnabled() {
        return Boolean.TRUE.equals(enableApiWatermark);
    }

    /**
     * 获取目标基础URL
     */
    public String getTargetBaseUrl() {
        if (targetHost == null || targetHost.trim().isEmpty()) {
            throw new IllegalStateException("目标主机地址不能为空");
        }

        String protocol = targetProtocol != null ? targetProtocol : "http";
        Integer port = targetPort != null ? targetPort : ("https".equals(protocol) ? 443 : 80);

        StringBuilder url = new StringBuilder();
        url.append(protocol).append("://").append(targetHost.trim());

        // 只有非标准端口才添加端口号
        if (!"http".equals(protocol) || port != 80) {
            if (!"https".equals(protocol) || port != 443) {
                url.append(":").append(port);
            }
        }

        return url.toString();
    }
}
