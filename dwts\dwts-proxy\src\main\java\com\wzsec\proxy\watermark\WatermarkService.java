package com.wzsec.proxy.watermark;

import com.wzsec.modules.domain.WebProxyconfig;
import com.wzsec.modules.domain.WebProxyrecord;
import com.wzsec.proxy.handler.ProxyRequestHandler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;

/**
 * 水印服务
 * 负责协调页面水印和API暗水印的加注
 *
 * <AUTHOR> Team
 * @date 2025/08/06
 */
@Slf4j
@Service
public class WatermarkService {

    @Autowired
    private ProxyRequestHandler requestHandler;

    /**
     * 处理水印加注
     */
    public byte[] processWatermark(byte[] originalContent,
                                 HttpServletRequest request,
                                 WebProxyconfig config,
                                 WebProxyrecord record) {
        
        if (originalContent == null || originalContent.length == 0) {
            return originalContent;
        }

        try {
            // 判断请求类型并决定水印处理策略
            if (requestHandler.isPageRequest(request) && 
                Boolean.TRUE.equals(config.getEnablePageWatermark())) {
                
                // 页面水印处理
                return processPageWatermark(originalContent, request, config, record);
                
            } else if (requestHandler.isApiRequest(request, config.getApiPathPatterns()) && 
                      Boolean.TRUE.equals(config.getEnableApiWatermark())) {
                
                // API暗水印处理
                return processApiWatermark(originalContent, request, config, record);
                
            } else {
                // 不需要水印处理
                record.setWatermarkAdded(false);
                record.setWatermarkType("NONE");
                return originalContent;
            }
            
        } catch (Exception e) {
            log.error("水印处理失败", e);
            record.setWatermarkAdded(false);
            record.setWatermarkType("ERROR");
            record.setErrorMessage("水印处理失败: " + e.getMessage());
            return originalContent;
        }
    }

    /**
     * 处理页面水印
     */
    private byte[] processPageWatermark(byte[] content,
                                      HttpServletRequest request,
                                      WebProxyconfig config,
                                      WebProxyrecord record) {
        
        log.info("开始处理页面水印 - 配置: {}", config.getApplicationname());
        
        try {
            String html = new String(content, StandardCharsets.UTF_8);
            
            // 生成水印文本
            String watermarkText = generateWatermarkText(request, config);
            
            // 注入水印到HTML
            String watermarkedHtml = injectPageWatermark(html, watermarkText, config);
            
            // 更新记录
            record.setWatermarkAdded(true);
            record.setWatermarkType("PAGE");
            record.setWatermarkContent(watermarkText);
            
            log.info("页面水印处理完成 - 水印文本: {}", watermarkText);
            
            return watermarkedHtml.getBytes(StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            log.error("页面水印处理失败", e);
            record.setWatermarkAdded(false);
            record.setWatermarkType("PAGE_ERROR");
            return content;
        }
    }

    /**
     * 处理API暗水印
     */
    private byte[] processApiWatermark(byte[] content,
                                     HttpServletRequest request,
                                     WebProxyconfig config,
                                     WebProxyrecord record) {
        
        log.info("开始处理API暗水印 - 配置: {}", config.getApplicationname());
        
        try {
            String contentString = new String(content, StandardCharsets.UTF_8);
            
            // 生成水印信息
            String watermarkInfo = generateWatermarkText(request, config);
            
            // 注入暗水印
            String watermarkedContent = injectInvisibleWatermark(contentString, watermarkInfo, config);
            
            // 更新记录
            record.setWatermarkAdded(true);
            record.setWatermarkType("API_INVISIBLE");
            record.setWatermarkContent(watermarkInfo);
            
            log.info("API暗水印处理完成 - 水印信息: {}", watermarkInfo);
            
            return watermarkedContent.getBytes(StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            log.error("API暗水印处理失败", e);
            record.setWatermarkAdded(false);
            record.setWatermarkType("API_ERROR");
            return content;
        }
    }

    /**
     * 生成水印文本
     */
    private String generateWatermarkText(HttpServletRequest request, WebProxyconfig config) {
        String template = config.getWatermarkText();
        if (template == null || template.trim().isEmpty()) {
            template = "DWTS_{IP}_{DATE}";
        }
        
        // 获取客户端IP
        String clientIp = getClientIpAddress(request);
        
        // 获取当前时间
        String currentDate = String.valueOf(System.currentTimeMillis() / 1000);
        
        // 替换模板变量
        String watermarkText = template
                .replace("{IP}", clientIp)
                .replace("{DATE}", currentDate)
                .replace("{TIME}", currentDate)
                .replace("{USER}", request.getRemoteUser() != null ? request.getRemoteUser() : "anonymous")
                .replace("{SESSION}", request.getSession(false) != null ? request.getSession().getId() : "nosession");
        
        return watermarkText;
    }

    /**
     * 注入页面水印
     */
    private String injectPageWatermark(String html, String watermarkText, WebProxyconfig config) {
        // 构建水印样式
        String watermarkStyle = buildWatermarkStyle(watermarkText, config);
        
        // 构建水印脚本
        String watermarkScript = buildWatermarkScript(config);
        
        // 注入到HTML中
        String result = html;
        
        // 在</head>前注入样式
        if (result.contains("</head>")) {
            result = result.replace("</head>", watermarkStyle + "\n</head>");
        } else if (result.contains("<html")) {
            // 如果没有head标签，在html标签后添加
            result = result.replaceFirst("<html[^>]*>", "$0\n<head>" + watermarkStyle + "</head>");
        }
        
        // 在</body>前注入脚本
        if (result.contains("</body>")) {
            result = result.replace("</body>", watermarkScript + "\n</body>");
        } else {
            // 如果没有body标签，在末尾添加
            result = result + watermarkScript;
        }
        
        return result;
    }

    /**
     * 构建水印样式
     */
    private String buildWatermarkStyle(String watermarkText, WebProxyconfig config) {
        // 获取配置参数
        Double opacity = config.getWatermarkOpacity() != null ? config.getWatermarkOpacity() : 0.3;
        String color = config.getWatermarkColor() != null ? config.getWatermarkColor() : "#999999";
        Double angle = config.getWatermarkAngle() != null ? config.getWatermarkAngle() : -30.0;
        Integer width = config.getWatermarkWidth() != null ? config.getWatermarkWidth() : 300;
        Integer height = config.getWatermarkHeight() != null ? config.getWatermarkHeight() : 150;
        
        return String.format(
            "<style type=\"text/css\">\n" +
            ".dwts-watermark {\n" +
            "    position: fixed;\n" +
            "    top: 0;\n" +
            "    left: 0;\n" +
            "    width: 100%%;\n" +
            "    height: 100%%;\n" +
            "    pointer-events: none;\n" +
            "    z-index: 9999;\n" +
            "    background-image: url('data:image/svg+xml;base64,%s');\n" +
            "    background-repeat: repeat;\n" +
            "    opacity: %.2f;\n" +
            "}\n" +
            "</style>",
            generateWatermarkSvg(watermarkText, color, angle, width, height),
            opacity
        );
    }

    /**
     * 构建水印脚本
     */
    private String buildWatermarkScript(WebProxyconfig config) {
        return 
            "<script type=\"text/javascript\">\n" +
            "(function() {\n" +
            "    function createWatermark() {\n" +
            "        var watermark = document.createElement('div');\n" +
            "        watermark.className = 'dwts-watermark';\n" +
            "        watermark.id = 'dwts-watermark-' + Date.now();\n" +
            "        document.body.appendChild(watermark);\n" +
            "        return watermark;\n" +
            "    }\n" +
            "    \n" +
            "    function initWatermark() {\n" +
            "        if (document.querySelector('.dwts-watermark')) return;\n" +
            "        createWatermark();\n" +
            "    }\n" +
            "    \n" +
            "    if (document.readyState === 'loading') {\n" +
            "        document.addEventListener('DOMContentLoaded', initWatermark);\n" +
            "    } else {\n" +
            "        initWatermark();\n" +
            "    }\n" +
            "})();\n" +
            "</script>";
    }

    /**
     * 生成水印SVG
     */
    private String generateWatermarkSvg(String text, String color, Double angle, Integer width, Integer height) {
        String svgContent = String.format(
            "<svg xmlns='http://www.w3.org/2000/svg' width='%d' height='%d' viewBox='0 0 %d %d'>" +
            "<text x='50%%' y='50%%' font-family='Arial, sans-serif' font-size='16' " +
            "fill='%s' text-anchor='middle' dominant-baseline='middle' " +
            "transform='rotate(%.1f %d %d)'>%s</text>" +
            "</svg>",
            width, height, width, height, color, angle, width/2, height/2, text
        );
        
        return java.util.Base64.getEncoder().encodeToString(svgContent.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 注入不可见水印
     */
    private String injectInvisibleWatermark(String content, String watermarkInfo, WebProxyconfig config) {
        // 这里实现简化的不可见字符水印
        // 在实际应用中，这里会调用 dwts-engine 的水印处理能力
        
        // 简单的零宽字符水印实现
        String watermarkCode = encodeToZeroWidthChars(watermarkInfo);
        
        // 在JSON或XML内容中嵌入零宽字符
        if (content.trim().startsWith("{") || content.trim().startsWith("[")) {
            // JSON内容
            return injectIntoJson(content, watermarkCode);
        } else if (content.trim().startsWith("<")) {
            // XML内容
            return injectIntoXml(content, watermarkCode);
        } else {
            // 纯文本内容
            return content + watermarkCode;
        }
    }

    /**
     * 编码为零宽字符
     */
    private String encodeToZeroWidthChars(String text) {
        // 简化实现：将文本转换为零宽字符序列
        StringBuilder result = new StringBuilder();
        for (char c : text.toCharArray()) {
            // 使用零宽字符编码
            result.append('\u200B'); // 零宽空格
            if (c % 2 == 0) {
                result.append('\u200C'); // 零宽非连接符
            } else {
                result.append('\u200D'); // 零宽连接符
            }
        }
        return result.toString();
    }

    /**
     * 在JSON中注入水印
     */
    private String injectIntoJson(String json, String watermark) {
        // 在JSON字符串值中注入零宽字符
        return json.replaceFirst("\"([^\"]+)\"", "\"$1" + watermark + "\"");
    }

    /**
     * 在XML中注入水印
     */
    private String injectIntoXml(String xml, String watermark) {
        // 在XML文本节点中注入零宽字符
        return xml.replaceFirst(">([^<]+)<", ">$1" + watermark + "<");
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
