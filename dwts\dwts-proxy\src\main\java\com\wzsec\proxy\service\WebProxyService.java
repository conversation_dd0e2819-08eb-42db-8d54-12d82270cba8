package com.wzsec.proxy.service;

import com.wzsec.modules.domain.WebProxyconfig;
import com.wzsec.modules.domain.WebProxyrecord;
import com.wzsec.modules.repository.WebProxyconfigRepository;
import com.wzsec.modules.repository.WebProxyrecordRepository;
import com.wzsec.proxy.handler.ProxyRequestHandler;
import com.wzsec.proxy.watermark.WatermarkService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.Enumeration;

/**
 * Web代理服务
 * 负责处理HTTP请求的代理转发和水印加注
 *
 * <AUTHOR> Team
 * @date 2025/08/06
 */
@Slf4j
@Service
public class WebProxyService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private WebProxyconfigRepository configRepository;

    @Autowired
    private WebProxyrecordRepository recordRepository;

    @Autowired
    private ProxyRequestHandler requestHandler;

    @Autowired
    private WatermarkService watermarkService;

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    /**
     * 处理代理请求
     */
    public ResponseEntity<?> handleProxyRequest(HttpServletRequest request,
                                                HttpServletResponse response,
                                                byte[] body) throws Exception {
        long startTime = System.currentTimeMillis();
        
        // 1. 根据端口查找代理配置
        WebProxyconfig config = findProxyConfig(request);
        if (config == null) {
            log.warn("未找到端口 {} 的代理配置", request.getServerPort());
            return ResponseEntity.notFound().build();
        }

        // 2. 检查配置是否有效
        if (!config.isValid()) {
            log.warn("代理配置无效: {}", config.getApplicationname());
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).build();
        }

        // 3. 创建访问记录
        WebProxyrecord record = createProxyRecord(request, config);

        try {
            // 4. 构建目标URL
            String targetUrl = buildTargetUrl(request, config);
            log.debug("代理请求: {} {} -> {}", request.getMethod(), request.getRequestURI(), targetUrl);

            // 5. 构建请求头
            HttpHeaders headers = buildRequestHeaders(request);

            // 6. 构建请求实体
            HttpEntity<byte[]> entity = new HttpEntity<>(body, headers);

            // 7. 转发请求到目标服务器
            ResponseEntity<byte[]> targetResponse = restTemplate.exchange(
                    targetUrl,
                    HttpMethod.valueOf(request.getMethod()),
                    entity,
                    byte[].class
            );

            // 8. 处理响应（包括水印加注）
            ResponseEntity<?> processedResponse = processResponse(
                    targetResponse, request, config, record);

            // 9. 更新访问记录
            updateProxyRecord(record, targetResponse, processedResponse, startTime);

            return processedResponse;

        } catch (Exception e) {
            log.error("代理请求处理失败: {} {} - {}", 
                     request.getMethod(), request.getRequestURI(), e.getMessage(), e);
            
            // 更新错误记录
            record.setExecutingstate("失败");
            record.setErrorMessage(e.getMessage());
            recordRepository.save(record);
            
            throw e;
        }
    }

    /**
     * 根据端口查找代理配置
     */
    private WebProxyconfig findProxyConfig(HttpServletRequest request) {
        int serverPort = request.getServerPort();
        return configRepository.findByProxyportAndIsvalid(String.valueOf(serverPort), "0");
    }

    /**
     * 创建代理访问记录
     */
    private WebProxyrecord createProxyRecord(HttpServletRequest request, WebProxyconfig config) {
        WebProxyrecord record = new WebProxyrecord();
        record.setProxyConfigId(config.getId().longValue());
        record.setRequestip(getClientIpAddress(request));
        record.setRequestPort(request.getServerPort());
        record.setRequestPath(request.getRequestURI());
        record.setRequestMethod(request.getMethod());
        record.setApplicationname(config.getApplicationname());
        record.setWebseverip(config.getRemoteaddr());
        record.setUserAgent(request.getHeader("User-Agent"));
        record.setReferer(request.getHeader("Referer"));
        record.setSessionId(request.getSession(false) != null ? request.getSession().getId() : null);
        record.setRequesttime(new Timestamp(System.currentTimeMillis()));
        record.setExecutingstate("处理中");
        
        return recordRepository.save(record);
    }

    /**
     * 构建目标URL
     */
    private String buildTargetUrl(HttpServletRequest request, WebProxyconfig config) {
        String baseUrl = config.getTargetBaseUrl();
        String requestUri = request.getRequestURI();
        String queryString = request.getQueryString();
        
        StringBuilder targetUrl = new StringBuilder(baseUrl);
        targetUrl.append(requestUri);
        
        if (queryString != null && !queryString.isEmpty()) {
            targetUrl.append("?").append(queryString);
        }
        
        return targetUrl.toString();
    }

    /**
     * 构建请求头
     */
    private HttpHeaders buildRequestHeaders(HttpServletRequest request) {
        HttpHeaders headers = new HttpHeaders();
        
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            
            // 跳过一些不应该转发的头
            if (shouldSkipHeader(headerName)) {
                continue;
            }
            
            String headerValue = request.getHeader(headerName);
            headers.add(headerName, headerValue);
        }
        
        return headers;
    }

    /**
     * 判断是否应该跳过某个请求头
     */
    private boolean shouldSkipHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.equals("host") ||
               lowerName.equals("content-length") ||
               lowerName.equals("connection") ||
               lowerName.equals("upgrade") ||
               lowerName.equals("proxy-connection") ||
               lowerName.equals("proxy-authenticate") ||
               lowerName.equals("proxy-authorization") ||
               lowerName.equals("te") ||
               lowerName.equals("trailers") ||
               lowerName.equals("transfer-encoding");
    }

    /**
     * 处理响应（包括水印加注）
     */
    private ResponseEntity<?> processResponse(ResponseEntity<byte[]> response,
                                            HttpServletRequest request,
                                            WebProxyconfig config,
                                            WebProxyrecord record) throws Exception {
        
        byte[] originalContent = response.getBody();
        if (originalContent == null || originalContent.length == 0) {
            return response;
        }

        // 更新记录信息
        record.setResponseStatus(response.getStatusCodeValue());
        record.setResponseContentType(getResponseContentType(response));
        record.setResponseSize((long) originalContent.length);

        // 判断请求类型
        String requestType = determineRequestType(request, response);
        record.setRequestType(requestType);

        // 根据请求类型和配置决定是否加注水印
        byte[] processedContent = watermarkService.processWatermark(
                originalContent, request, config, record);

        // 构建响应头
        HttpHeaders responseHeaders = new HttpHeaders();
        copyResponseHeaders(response.getHeaders(), responseHeaders);

        // 如果内容被修改，更新Content-Length
        if (processedContent != originalContent) {
            responseHeaders.setContentLength(processedContent.length);
        }

        return new ResponseEntity<>(processedContent, responseHeaders, response.getStatusCode());
    }

    /**
     * 判断请求类型
     */
    private String determineRequestType(HttpServletRequest request, ResponseEntity<byte[]> response) {
        String contentType = getResponseContentType(response);
        String path = request.getRequestURI();
        
        if (contentType != null) {
            if (contentType.contains("text/html")) {
                return "PAGE";
            } else if (contentType.contains("application/json") || 
                      contentType.contains("application/xml") ||
                      contentType.contains("text/xml")) {
                return "API";
            }
        }
        
        // 根据路径判断
        if (path.contains("/api/") || path.contains("/rest/") || path.contains("/service/")) {
            return "API";
        }
        
        return "RESOURCE";
    }

    /**
     * 获取响应内容类型
     */
    private String getResponseContentType(ResponseEntity<byte[]> response) {
        MediaType contentType = response.getHeaders().getContentType();
        return contentType != null ? contentType.toString() : null;
    }

    /**
     * 复制响应头
     */
    private void copyResponseHeaders(HttpHeaders source, HttpHeaders target) {
        for (String headerName : source.keySet()) {
            if (!shouldSkipResponseHeader(headerName)) {
                target.put(headerName, source.get(headerName));
            }
        }
    }

    /**
     * 判断是否应该跳过某个响应头
     */
    private boolean shouldSkipResponseHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.equals("content-length") ||
               lowerName.equals("transfer-encoding") ||
               lowerName.equals("connection") ||
               lowerName.equals("upgrade");
    }

    /**
     * 更新代理记录
     */
    private void updateProxyRecord(WebProxyrecord record, 
                                 ResponseEntity<byte[]> targetResponse,
                                 ResponseEntity<?> processedResponse,
                                 long startTime) {
        long processTime = System.currentTimeMillis() - startTime;
        record.setProcessTime(processTime);
        record.setExecutingstate("成功");
        recordRepository.save(record);
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
