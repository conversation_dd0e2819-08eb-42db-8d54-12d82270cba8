package com.wzsec.modules.component;

import cn.hutool.core.lang.Console;
import com.wzsec.modules.domain.WebProxyconfig;
import com.wzsec.modules.repository.WebProxyconfigRepository;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 数据源代理配置
 *
 * <AUTHOR>
 * @date 2023-06-14
 */
@Component
public class WebProxyConfigComponent {

    @Value("${dataSource.proxy.ip}")
    private String hostAddress;

    @Value("${server.port}")
    private String serverPort;

    private final WebProxyconfigRepository webProxyconfigRepository;

    public WebProxyConfigComponent(WebProxyconfigRepository webProxyconfigRepository) {
        this.webProxyconfigRepository = webProxyconfigRepository;
    }

    /**
     * 获取代理配置(由前端配置取值)
     */
    public List<WebProxyconfig> webProxyConfigList() {
        //配置文件取本机IP字符串
        Console.log("====>获取到的端口为:{}", serverPort);
        //代理引擎服务器ip:port
        String engineServer = hostAddress + ":" + serverPort;
        //通过引擎查询使用需要代理的资源
        List<WebProxyconfig> webProxyConfigList = webProxyconfigRepository.findWebProxyConfigByEngine(engineServer);
        return webProxyConfigList;
    }

}
