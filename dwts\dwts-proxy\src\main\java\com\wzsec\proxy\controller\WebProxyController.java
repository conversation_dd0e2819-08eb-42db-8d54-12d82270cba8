package com.wzsec.proxy.controller;

import com.wzsec.proxy.service.WebProxyService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Web代理控制器
 * 处理所有代理请求
 *
 * <AUTHOR> Team
 * @date 2025/08/06
 */
@Slf4j
@RestController
public class WebProxyController {

    @Autowired
    private WebProxyService webProxyService;

    /**
     * 处理所有HTTP请求的代理转发
     * 支持GET、POST、PUT、DELETE、PATCH、HEAD、OPTIONS等所有HTTP方法
     *
     * @param request  HTTP请求
     * @param response HTTP响应
     * @param body     请求体（可选）
     * @return 代理响应
     */
    @RequestMapping(value = "/**", 
                   method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, 
                            RequestMethod.DELETE, RequestMethod.PATCH, RequestMethod.HEAD, 
                            RequestMethod.OPTIONS, RequestMethod.TRACE})
    public ResponseEntity<?> proxyRequest(HttpServletRequest request,
                                        HttpServletResponse response,
                                        @RequestBody(required = false) byte[] body) {
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 记录请求信息
            if (log.isDebugEnabled()) {
                log.debug("代理请求: {} {} from {}", 
                         request.getMethod(), 
                         request.getRequestURI(), 
                         getClientIpAddress(request));
            }
            
            // 处理代理请求
            ResponseEntity<?> result = webProxyService.handleProxyRequest(request, response, body);
            
            // 记录处理时间
            long processTime = System.currentTimeMillis() - startTime;
            if (log.isDebugEnabled()) {
                log.debug("代理请求处理完成，耗时: {}ms, 状态码: {}", 
                         processTime, 
                         result.getStatusCodeValue());
            }
            
            return result;
            
        } catch (Exception e) {
            long processTime = System.currentTimeMillis() - startTime;
            log.error("代理请求处理失败: {} {} (耗时: {}ms) - {}", 
                     request.getMethod(), 
                     request.getRequestURI(), 
                     processTime,
                     e.getMessage(), e);
            
            // 返回错误响应
            return ResponseEntity.internalServerError()
                    .header("X-DWTS-Error", "Proxy request failed")
                    .header("X-DWTS-Error-Message", e.getMessage())
                    .body("代理请求处理失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/dwts/proxy/health")
    public ResponseEntity<?> healthCheck() {
        return ResponseEntity.ok()
                .header("X-DWTS-Service", "Web Proxy")
                .header("X-DWTS-Status", "OK")
                .body("Web代理服务运行正常");
    }

    /**
     * 获取代理状态信息
     */
    @GetMapping("/dwts/proxy/status")
    public ResponseEntity<?> getStatus() {
        try {
            // 这里可以添加更详细的状态检查逻辑
            return ResponseEntity.ok()
                    .header("X-DWTS-Service", "Web Proxy")
                    .header("X-DWTS-Version", "1.0.0")
                    .body("代理服务状态正常");
        } catch (Exception e) {
            log.error("获取代理状态失败", e);
            return ResponseEntity.internalServerError()
                    .body("获取代理状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
