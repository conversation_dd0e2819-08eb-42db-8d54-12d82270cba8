package com.wzsec.modules.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@Entity
@Data
@Table(name = "dwts_web_proxyrecord")
public class WebProxyrecord implements Serializable {

    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 请求IP
     */
    @Column(name = "requestip")
    // @NotBlank
    private String requestip;

    /**
     * 请求时间
     */
    @Column(name = "requesttime")
    private Timestamp requesttime;

    /**
     * 执行状态(成功,失败)
     */
    @Column(name = "executingstate")
    private String executingstate;

    /**
     * web应用名称
     */
    @Column(name = "applicationname")
    // @NotBlank
    private String applicationname;

    /**
     * web应用IP
     */
    @Column(name = "webseverip")
    // @NotBlank
    private String webseverip;

    /**
     * 水印信息
     */
    @Column(name = "webwatermark")
    private String webwatermark;

    /**
     * 备注
     */
    @Column(name = "note")
    private String note;

    /**
     * 插入时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    // ========== 新增水印代理相关字段 ==========

    /**
     * 代理配置ID
     */
    @Column(name = "proxy_config_id")
    private Long proxyConfigId;

    /**
     * 请求端口
     */
    @Column(name = "request_port")
    private Integer requestPort;

    /**
     * 请求路径
     */
    @Column(name = "request_path")
    private String requestPath;

    /**
     * 请求方法
     */
    @Column(name = "request_method")
    private String requestMethod;

    /**
     * 请求类型 (PAGE/API/RESOURCE)
     */
    @Column(name = "request_type")
    private String requestType;

    /**
     * 响应状态码
     */
    @Column(name = "response_status")
    private Integer responseStatus;

    /**
     * 响应内容类型
     */
    @Column(name = "response_content_type")
    private String responseContentType;

    /**
     * 响应大小（字节）
     */
    @Column(name = "response_size")
    private Long responseSize;

    /**
     * 是否添加了水印
     */
    @Column(name = "watermark_added")
    private Boolean watermarkAdded;

    /**
     * 水印类型 (PAGE/JSON/XML/HEADER)
     */
    @Column(name = "watermark_type")
    private String watermarkType;

    /**
     * 水印内容
     */
    @Column(name = "watermark_content")
    private String watermarkContent;

    /**
     * 处理耗时（毫秒）
     */
    @Column(name = "process_time")
    private Long processTime;

    /**
     * 用户代理
     */
    @Column(name = "user_agent")
    private String userAgent;

    /**
     * 引用页面
     */
    @Column(name = "referer")
    private String referer;

    /**
     * 会话ID
     */
    @Column(name = "session_id")
    private String sessionId;

    /**
     * 错误信息
     */
    @Column(name = "error_message")
    private String errorMessage;

    // ========== 兼容性方法 ==========

    /**
     * 获取请求IP（兼容原字段名）
     */
    public String getRequestIp() {
        return requestip;
    }

    /**
     * 设置请求IP（兼容原字段名）
     */
    public void setRequestIp(String requestIp) {
        this.requestip = requestIp;
    }

    /**
     * 获取应用名称（兼容原字段名）
     */
    public String getApplicationName() {
        return applicationname;
    }

    /**
     * 设置应用名称（兼容原字段名）
     */
    public void setApplicationName(String applicationName) {
        this.applicationname = applicationName;
    }

    /**
     * 获取Web服务器IP（兼容原字段名）
     */
    public String getWebServerIp() {
        return webseverip;
    }

    /**
     * 设置Web服务器IP（兼容原字段名）
     */
    public void setWebServerIp(String webServerIp) {
        this.webseverip = webServerIp;
    }

    /**
     * 获取Web水印（兼容原字段名）
     */
    public String getWebWatermark() {
        return webwatermark;
    }

    /**
     * 设置Web水印（兼容原字段名）
     */
    public void setWebWatermark(String webWatermark) {
        this.webwatermark = webWatermark;
    }

    public void copy(WebProxyrecord source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }
}