package com.wzsec;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.net.InetAddress;
import java.net.UnknownHostException;

@Slf4j
@EnableAsync
@RestController
@SpringBootApplication(exclude = {SecurityAutoConfiguration.class, MongoAutoConfiguration.class, MongoDataAutoConfiguration.class})
@EnableTransactionManagement
public class DWTSProxyRun {

    private static DWTSProxyRun dwtsProxyRun;

    @Autowired
    private Environment environment;

    @PostConstruct
    public void init() {
        dwtsProxyRun = this;
        dwtsProxyRun.environment = this.environment;
    }

    public static void main(String[] args) {

        try {
            ConfigurableApplicationContext context = SpringApplication.run(DWTSProxyRun.class, args);
            Environment env = context.getEnvironment();

            String protocol = "http";
            if (env.getProperty("server.ssl.key-store") != null) {
                protocol = "https";
            }

            String serverPort = env.getProperty("server.port", "8080");
            String contextPath = env.getProperty("server.servlet.context-path", "");
            String hostAddress = "localhost";

            try {
                hostAddress = InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException e) {
                log.warn("无法获取本机IP地址，使用localhost");
            }

            log.info("\n----------------------------------------------------------\n" +
                            "  DWTS Web代理服务启动成功！\n" +
                            "  访问地址: \n" +
                            "    本地访问: {}://localhost:{}{}\n" +
                            "    外部访问: {}://{}:{}{}\n" +
                            "  配置文件: {}\n" +
                            "  数据库: {}\n" +
                            "----------------------------------------------------------",
                    protocol, serverPort, contextPath,
                    protocol, hostAddress, serverPort, contextPath,
                    env.getActiveProfiles().length == 0 ? "default" : String.join(",", env.getActiveProfiles()),
                    env.getProperty("spring.datasource.url", "未配置"));

        } catch (Exception e) {
            log.error("应用启动失败", e);
            System.exit(1);
        }

        System.out.println();
        String[] activeProfiles = dwtsProxyRun.environment.getActiveProfiles();
        System.out.print("--------------- 【数据水印代理引擎】生效配置文件为: ");
        for (String profile : activeProfiles) {
            System.out.print(profile + " ---------------");
        }
        System.out.println();

    }

}
