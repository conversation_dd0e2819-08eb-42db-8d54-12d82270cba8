package com.wzsec.modules.service.mapper;

import com.wzsec.base.BaseMapper;
import com.wzsec.modules.domain.WebProxywmconfig;
import com.wzsec.modules.service.dto.WebProxywmconfigDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WebProxywmconfigMapper extends BaseMapper<WebProxywmconfigDto, WebProxywmconfig> {

}