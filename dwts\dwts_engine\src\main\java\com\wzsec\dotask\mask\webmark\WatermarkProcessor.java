package com.wzsec.dotask.mask.webmark;

import javax.servlet.http.HttpServletRequest;

/**
 * 水印处理器接口
 * 定义水印处理的基本方法
 *
 * <AUTHOR> Team
 * @date 2025/08/06
 */
public interface WatermarkProcessor {

    /**
     * 获取处理器名称
     *
     * @return 处理器名称
     */
    String getProcessorName();

    /**
     * 获取水印类型
     *
     * @return 水印类型
     */
    String getWatermarkType();

    /**
     * 检查是否能处理指定的内容类型
     *
     * @param contentType 内容类型
     * @return 是否能处理
     */
    boolean canHandle(String contentType);

    /**
     * 处理水印
     *
     * @param content     原始内容
     * @param contentType 内容类型
     * @param request     HTTP请求
     * @param config      水印配置
     * @return 处理后的内容
     */
    byte[] processWatermark(byte[] content, String contentType, 
                          HttpServletRequest request, WatermarkConfig config);

    /**
     * 提取水印
     *
     * @param content     包含水印的内容
     * @param contentType 内容类型
     * @return 提取的水印信息
     */
    WatermarkInfo extractWatermark(byte[] content, String contentType);
}
