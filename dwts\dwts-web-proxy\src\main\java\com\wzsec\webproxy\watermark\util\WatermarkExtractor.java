package com.wzsec.webproxy.watermark.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 水印提取和溯源工具类
 * 用于从带有暗水印的内容中提取水印信息，支持溯源追踪
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Component
public class WatermarkExtractor {

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 零宽字符定义（与InvisibleWatermarkProcessor保持一致）
    private static final char ZERO_WIDTH_SPACE = '\u200B';
    private static final char ZERO_WIDTH_NON_JOINER = '\u200C';
    private static final char ZERO_WIDTH_JOINER = '\u200D';
    private static final char WORD_JOINER = '\u2060';
    private static final char INVISIBLE_SEPARATOR = '\u2062';
    
    // 解码映射表
    private static final Map<Character, String> DECODING_MAP = new HashMap<>();
    static {
        DECODING_MAP.put(ZERO_WIDTH_SPACE, "00");
        DECODING_MAP.put(ZERO_WIDTH_NON_JOINER, "01");
        DECODING_MAP.put(ZERO_WIDTH_JOINER, "10");
        DECODING_MAP.put(WORD_JOINER, "11");
    }

    /**
     * 从内容中提取水印信息
     *
     * @param content 包含水印的内容
     * @param contentType 内容类型
     * @return 提取的水印信息列表
     */
    public List<ExtractedWatermark> extractWatermarks(String content, String contentType) {
        List<ExtractedWatermark> watermarks = new ArrayList<>();
        
        try {
            if (isJsonContent(contentType)) {
                watermarks.addAll(extractFromJson(content));
            } else if (isXmlContent(contentType)) {
                watermarks.addAll(extractFromXml(content));
            } else {
                watermarks.addAll(extractFromText(content));
            }
            
            log.info("从内容中提取到 {} 个水印", watermarks.size());
            
        } catch (Exception e) {
            log.error("水印提取失败", e);
        }
        
        return watermarks;
    }

    /**
     * 从JSON内容中提取水印
     */
    private List<ExtractedWatermark> extractFromJson(String jsonContent) throws Exception {
        List<ExtractedWatermark> watermarks = new ArrayList<>();
        JsonNode rootNode = objectMapper.readTree(jsonContent);
        
        extractFromJsonNode(rootNode, "", watermarks);
        
        return watermarks;
    }

    /**
     * 递归从JSON节点中提取水印
     */
    private void extractFromJsonNode(JsonNode node, String path, List<ExtractedWatermark> watermarks) {
        if (node.isObject()) {
            node.fields().forEachRemaining(entry -> {
                String fieldPath = path.isEmpty() ? entry.getKey() : path + "." + entry.getKey();
                if (entry.getValue().isTextual()) {
                    List<WatermarkInfo> infos = extractInvisibleWatermarks(entry.getValue().asText());
                    for (WatermarkInfo info : infos) {
                        watermarks.add(new ExtractedWatermark(fieldPath, info));
                    }
                } else {
                    extractFromJsonNode(entry.getValue(), fieldPath, watermarks);
                }
            });
        } else if (node.isArray()) {
            for (int i = 0; i < node.size(); i++) {
                String arrayPath = path + "[" + i + "]";
                JsonNode element = node.get(i);
                if (element.isTextual()) {
                    List<WatermarkInfo> infos = extractInvisibleWatermarks(element.asText());
                    for (WatermarkInfo info : infos) {
                        watermarks.add(new ExtractedWatermark(arrayPath, info));
                    }
                } else {
                    extractFromJsonNode(element, arrayPath, watermarks);
                }
            }
        }
    }

    /**
     * 从XML内容中提取水印
     */
    private List<ExtractedWatermark> extractFromXml(String xmlContent) {
        List<ExtractedWatermark> watermarks = new ArrayList<>();
        
        // 使用正则表达式提取XML文本节点
        Pattern textNodePattern = Pattern.compile(">([^<]+)<");
        Matcher matcher = textNodePattern.matcher(xmlContent);
        
        int nodeIndex = 0;
        while (matcher.find()) {
            String textContent = matcher.group(1);
            List<WatermarkInfo> infos = extractInvisibleWatermarks(textContent);
            for (WatermarkInfo info : infos) {
                watermarks.add(new ExtractedWatermark("xml.textNode[" + nodeIndex + "]", info));
            }
            nodeIndex++;
        }
        
        return watermarks;
    }

    /**
     * 从纯文本中提取水印
     */
    private List<ExtractedWatermark> extractFromText(String textContent) {
        List<ExtractedWatermark> watermarks = new ArrayList<>();
        List<WatermarkInfo> infos = extractInvisibleWatermarks(textContent);
        
        for (WatermarkInfo info : infos) {
            watermarks.add(new ExtractedWatermark("text", info));
        }
        
        return watermarks;
    }

    /**
     * 从文本中提取不可见水印
     */
    private List<WatermarkInfo> extractInvisibleWatermarks(String text) {
        List<WatermarkInfo> watermarks = new ArrayList<>();
        
        if (text == null || text.isEmpty()) {
            return watermarks;
        }
        
        try {
            // 查找零宽字符序列
            List<String> zeroWidthSequences = findZeroWidthSequences(text);

            log.debug("找到 {} 个零宽字符序列，开始解码", zeroWidthSequences.size());

            for (int i = 0; i < zeroWidthSequences.size(); i++) {
                String sequence = zeroWidthSequences.get(i);
                log.debug("处理序列 {}/{}: 长度={} 字符", i + 1, zeroWidthSequences.size(), sequence.length());

                try {
                    String decodedData = decodeFromZeroWidthChars(sequence);
                    if (decodedData != null && !decodedData.trim().isEmpty()) {
                        log.debug("序列 {} 解码成功: '{}'", i + 1, decodedData);

                        WatermarkInfo info = parseWatermarkData(decodedData);
                        if (info != null) {
                            watermarks.add(info);
                            log.info("成功解析水印信息: 用户={}, IP={}, 时间={}",
                                   info.getUserId(), info.getIpAddress(), info.getTimestamp());
                        } else {
                            log.debug("序列 {} 解码数据无法解析为水印信息: '{}'", i + 1, decodedData);

                            // 尝试其他解析方式
                            tryAlternativeDecoding(sequence, watermarks);
                        }
                    } else {
                        log.debug("序列 {} 解码结果为空", i + 1);
                    }
                } catch (Exception e) {
                    log.debug("解码序列 {} 失败: {}", i + 1, e.getMessage());
                }
            }

            // 如果没有成功解码任何水印，尝试合并所有序列
            if (watermarks.isEmpty() && !zeroWidthSequences.isEmpty()) {
                log.debug("尝试合并所有零宽字符序列进行解码");
                tryMergedDecoding(zeroWidthSequences, watermarks);
            }

        } catch (Exception e) {
            log.warn("提取不可见水印失败: {}", e.getMessage());
        }
        
        return watermarks;
    }

    /**
     * 查找零宽字符序列（改进版本）
     */
    private List<String> findZeroWidthSequences(String text) {
        List<String> sequences = new ArrayList<>();

        log.debug("开始查找零宽字符序列，文本长度: {}", text.length());

        // 方法1：查找被分隔符包围的完整序列
        sequences.addAll(findDelimitedSequences(text));

        // 方法2：如果没有找到完整序列，尝试收集所有零宽字符作为一个序列
        if (sequences.isEmpty()) {
            log.debug("未找到分隔符包围的序列，尝试收集所有零宽字符");
            String allZeroWidthChars = extractAllZeroWidthChars(text);
            if (!allZeroWidthChars.isEmpty()) {
                sequences.add(allZeroWidthChars);
                log.debug("收集到所有零宽字符作为单一序列，长度: {} 字符", allZeroWidthChars.length());
            }
        }

        // 方法3：如果仍然没有找到，尝试查找连续的零宽字符块
        if (sequences.isEmpty()) {
            log.debug("尝试查找连续的零宽字符块");
            sequences.addAll(findContinuousZeroWidthBlocks(text));
        }

        log.info("总共发现 {} 个零宽字符序列", sequences.size());

        // 详细的调试信息
        for (int i = 0; i < sequences.size(); i++) {
            String seq = sequences.get(i);
            log.debug("序列 {}: 长度={}, 内容={}", i + 1, seq.length(),
                     seq.chars().mapToObj(ch -> String.format("\\u%04X", ch)).collect(java.util.stream.Collectors.joining("")));
        }

        return sequences;
    }

    /**
     * 查找被分隔符包围的完整序列
     */
    private List<String> findDelimitedSequences(String text) {
        List<String> sequences = new ArrayList<>();
        StringBuilder currentSequence = new StringBuilder();
        boolean inSequence = false;

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);

            if (c == INVISIBLE_SEPARATOR) {
                if (inSequence) {
                    // 结束当前序列
                    if (currentSequence.length() > 0) {
                        String sequence = currentSequence.toString();
                        sequences.add(sequence);
                        log.debug("发现完整分隔序列，长度: {} 字符", sequence.length());
                        currentSequence.setLength(0);
                    }
                    inSequence = false;
                } else {
                    // 开始新序列
                    inSequence = true;
                    log.debug("开始新的分隔序列，位置: {}", i);
                }
            } else if (DECODING_MAP.containsKey(c) && inSequence) {
                currentSequence.append(c);
            }
        }

        return sequences;
    }

    /**
     * 查找连续的零宽字符块
     */
    private List<String> findContinuousZeroWidthBlocks(String text) {
        List<String> sequences = new ArrayList<>();
        StringBuilder currentSequence = new StringBuilder();

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);

            if (DECODING_MAP.containsKey(c)) {
                currentSequence.append(c);
            } else {
                // 遇到非零宽字符，结束当前序列
                if (currentSequence.length() > 0) {
                    String sequence = currentSequence.toString();
                    sequences.add(sequence);
                    log.debug("发现连续零宽字符块，长度: {} 字符", sequence.length());
                    currentSequence.setLength(0);
                }
            }
        }

        // 处理末尾的序列
        if (currentSequence.length() > 0) {
            String sequence = currentSequence.toString();
            sequences.add(sequence);
            log.debug("发现末尾零宽字符块，长度: {} 字符", sequence.length());
        }

        return sequences;
    }

    /**
     * 尝试其他解码方式
     */
    private void tryAlternativeDecoding(String sequence, List<WatermarkInfo> watermarks) {
        try {
            // 尝试反向解码
            String reversedSequence = new StringBuilder(sequence).reverse().toString();
            String decodedData = decodeFromZeroWidthChars(reversedSequence);
            if (decodedData != null && !decodedData.trim().isEmpty()) {
                WatermarkInfo info = parseWatermarkData(decodedData);
                if (info != null) {
                    watermarks.add(info);
                    log.debug("反向解码成功");
                    return;
                }
            }

            // 尝试跳过部分字符解码
            if (sequence.length() > 2) {
                for (int skip = 1; skip <= 2 && skip < sequence.length(); skip++) {
                    String skippedSequence = sequence.substring(skip);
                    decodedData = decodeFromZeroWidthChars(skippedSequence);
                    if (decodedData != null && !decodedData.trim().isEmpty()) {
                        WatermarkInfo info = parseWatermarkData(decodedData);
                        if (info != null) {
                            watermarks.add(info);
                            log.debug("跳过 {} 个字符后解码成功", skip);
                            return;
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.debug("其他解码方式失败: {}", e.getMessage());
        }
    }

    /**
     * 尝试合并所有序列进行解码
     */
    private void tryMergedDecoding(List<String> sequences, List<WatermarkInfo> watermarks) {
        try {
            // 合并所有序列
            StringBuilder merged = new StringBuilder();
            for (String seq : sequences) {
                merged.append(seq);
            }

            String mergedSequence = merged.toString();
            log.debug("合并序列长度: {} 字符", mergedSequence.length());

            String decodedData = decodeFromZeroWidthChars(mergedSequence);
            if (decodedData != null && !decodedData.trim().isEmpty()) {
                WatermarkInfo info = parseWatermarkData(decodedData);
                if (info != null) {
                    watermarks.add(info);
                    log.debug("合并序列解码成功");
                }
            }

        } catch (Exception e) {
            log.debug("合并序列解码失败: {}", e.getMessage());
        }
    }

    /**
     * 提取文本中所有的零宽字符（不考虑分隔符）
     */
    private String extractAllZeroWidthChars(String text) {
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if (DECODING_MAP.containsKey(c)) {
                result.append(c);
            }
        }

        log.debug("提取到 {} 个零宽字符", result.length());
        return result.toString();
    }

    /**
     * 从零宽字符解码为字符串（改进版本）
     */
    private String decodeFromZeroWidthChars(String zeroWidthSequence) {
        StringBuilder binaryBuilder = new StringBuilder();

        log.debug("开始解码零宽字符序列，长度: {} 字符", zeroWidthSequence.length());

        // 将零宽字符转换为二进制
        for (int i = 0; i < zeroWidthSequence.length(); i++) {
            char c = zeroWidthSequence.charAt(i);

            if (c == INVISIBLE_SEPARATOR) {
                log.debug("跳过分隔符，位置: {}", i);
                continue;
            }

            String bits = DECODING_MAP.get(c);
            if (bits != null) {
                binaryBuilder.append(bits);
                log.debug("字符 \\u{} -> {}", String.format("%04X", (int) c), bits);
            } else {
                log.warn("未知的零宽字符: \\u{}", String.format("%04X", (int) c));
            }
        }

        String binaryString = binaryBuilder.toString();
        log.debug("二进制字符串: {} (长度: {})", binaryString, binaryString.length());

        // 改进的长度检查和处理
        if (binaryString.length() < 8) {
            log.warn("二进制序列太短 ({}位)，跳过解码", binaryString.length());
            return "";
        }

        if (binaryString.length() % 8 != 0) {
            log.warn("二进制长度 {} 不是8的倍数，尝试补齐或截断", binaryString.length());

            // 尝试补齐到8的倍数
            int remainder = binaryString.length() % 8;
            int paddingNeeded = 8 - remainder;

            if (paddingNeeded <= 4) {
                // 如果需要补齐的位数不多，尝试补0
                StringBuilder padding = new StringBuilder();
                for (int j = 0; j < paddingNeeded; j++) {
                    padding.append("0");
                }
                binaryString = binaryString + padding.toString();
                log.debug("补齐后的二进制字符串: {} (长度: {})", binaryString, binaryString.length());
            } else {
                // 如果需要补齐的位数太多，截断到最近的8的倍数
                int newLength = (binaryString.length() / 8) * 8;
                binaryString = binaryString.substring(0, newLength);
                log.debug("截断后的二进制字符串: {} (长度: {})", binaryString, binaryString.length());
            }
        }

        return binaryToString(binaryString);
    }

    /**
     * 将二进制字符串转换为文本
     */
    private String binaryToString(String binaryString) {
        StringBuilder result = new StringBuilder();

        // 每8位二进制转换为一个字符
        for (int i = 0; i < binaryString.length(); i += 8) {
            if (i + 8 <= binaryString.length()) {
                String byteString = binaryString.substring(i, i + 8);
                try {
                    int charCode = Integer.parseInt(byteString, 2);
                    char decodedChar = (char) charCode;

                    // 过滤掉控制字符（除了常见的空白字符）
                    if (charCode >= 32 || charCode == 9 || charCode == 10 || charCode == 13) {
                        result.append(decodedChar);
                        log.debug("二进制 {} -> 字符码 {} -> 字符 '{}'", byteString, charCode,
                                 isPrintable(decodedChar) ? decodedChar : "\\u" + String.format("%04X", (int) decodedChar));
                    } else {
                        log.debug("跳过控制字符: 二进制 {} -> 字符码 {}", byteString, charCode);
                    }
                } catch (NumberFormatException e) {
                    log.warn("无法解析二进制字符串: {}", byteString);
                }
            }
        }

        String decodedText = result.toString();
        log.debug("解码结果: '{}'", decodedText);
        return decodedText;
    }

    /**
     * 判断字符是否可打印
     */
    private boolean isPrintable(char c) {
        return c >= 32 && c <= 126;
    }

    /**
     * 解析水印数据
     */
    private WatermarkInfo parseWatermarkData(String watermarkData) {
        if (watermarkData == null || watermarkData.trim().isEmpty()) {
            log.debug("水印数据为空");
            return null;
        }

        log.debug("尝试解析水印数据: '{}'", watermarkData);

        try {
            // 清理数据，移除可能的控制字符
            String cleanedData = watermarkData.replaceAll("[\\x00-\\x1F\\x7F]", "").trim();

            String[] parts = cleanedData.split("\\|");
            log.debug("水印数据分割结果: {} 部分", parts.length);

            if (parts.length < 4) {
                log.debug("水印数据部分不足，需要至少4部分，实际: {}", parts.length);
                return tryAlternativeParsingFormats(cleanedData);
            }

            String userId = parts[0].trim();
            String ipAddress = parts[1].trim();
            String timestampStr = parts[2].trim();
            String sessionId = parts[3].trim();

            // 验证基本字段
            if (userId.isEmpty() || ipAddress.isEmpty() || timestampStr.isEmpty()) {
                log.debug("基本字段为空: userId='{}', ipAddress='{}', timestamp='{}'",
                         userId, ipAddress, timestampStr);
                return null;
            }

            long timestamp;
            try {
                timestamp = Long.parseLong(timestampStr);
            } catch (NumberFormatException e) {
                log.debug("时间戳格式错误: '{}'", timestampStr);
                return null;
            }

            // 如果有校验和，验证它
            if (parts.length >= 5) {
                String checksum = parts[4].trim();
                String dataToVerify = String.join("|", Arrays.copyOf(parts, 4));
                if (!verifyChecksum(dataToVerify, checksum)) {
                    log.warn("水印校验和验证失败，但仍尝试解析");
                    // 不直接返回null，继续尝试解析
                }
            }

            WatermarkInfo info = WatermarkInfo.builder()
                .userId(userId)
                .ipAddress(ipAddress)
                .timestamp(timestamp)
                .sessionId(sessionId)
                .build();

            log.debug("成功解析水印信息: userId={}, ipAddress={}, timestamp={}, sessionId={}",
                     userId, ipAddress, timestamp, sessionId);
            return info;

        } catch (Exception e) {
            log.debug("解析水印数据失败: {}", e.getMessage());
            return tryAlternativeParsingFormats(watermarkData);
        }
    }

    /**
     * 尝试其他解析格式
     */
    private WatermarkInfo tryAlternativeParsingFormats(String watermarkData) {
        try {
            // 尝试不同的分隔符
            String[] separators = {",", ";", ":", " ", "\t"};

            for (String sep : separators) {
                String[] parts = watermarkData.split(Pattern.quote(sep));
                if (parts.length >= 4) {
                    log.debug("尝试使用分隔符 '{}' 解析", sep);
                    try {
                        String userId = parts[0].trim();
                        String ipAddress = parts[1].trim();
                        long timestamp = Long.parseLong(parts[2].trim());
                        String sessionId = parts[3].trim();

                        if (!userId.isEmpty() && !ipAddress.isEmpty()) {
                            return WatermarkInfo.builder()
                                .userId(userId)
                                .ipAddress(ipAddress)
                                .timestamp(timestamp)
                                .sessionId(sessionId)
                                .build();
                        }
                    } catch (Exception e) {
                        log.debug("使用分隔符 '{}' 解析失败: {}", sep, e.getMessage());
                    }
                }
            }

        } catch (Exception e) {
            log.debug("其他解析格式尝试失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 验证校验和
     */
    private boolean verifyChecksum(String data, String expectedChecksum) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(data.getBytes(StandardCharsets.UTF_8));
            String actualChecksum = Base64.getEncoder().encodeToString(hash).substring(0, 4);
            return actualChecksum.equals(expectedChecksum);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 生成溯源报告
     */
    public TraceReport generateTraceReport(List<ExtractedWatermark> watermarks) {
        TraceReport report = new TraceReport();
        report.setExtractTime(System.currentTimeMillis());
        report.setWatermarkCount(watermarks.size());
        
        Map<String, Integer> userStats = new HashMap<>();
        Map<String, Integer> ipStats = new HashMap<>();
        Set<String> sessions = new HashSet<>();
        
        for (ExtractedWatermark watermark : watermarks) {
            WatermarkInfo info = watermark.getWatermarkInfo();
            
            // 统计用户
            userStats.merge(info.getUserId(), 1, Integer::sum);
            
            // 统计IP
            ipStats.merge(info.getIpAddress(), 1, Integer::sum);
            
            // 收集会话
            sessions.add(info.getSessionId());
        }
        
        report.setUserStatistics(userStats);
        report.setIpStatistics(ipStats);
        report.setUniqueSessionCount(sessions.size());
        report.setWatermarksDetails(watermarks);
        
        return report;
    }

    private boolean isJsonContent(String contentType) {
        return contentType != null && 
               (contentType.toLowerCase().contains("application/json") ||
                contentType.toLowerCase().contains("text/json"));
    }

    private boolean isXmlContent(String contentType) {
        return contentType != null && 
               (contentType.toLowerCase().contains("application/xml") ||
                contentType.toLowerCase().contains("text/xml"));
    }

    /**
     * 提取的水印信息
     */
    public static class ExtractedWatermark {
        private String location;
        private WatermarkInfo watermarkInfo;
        
        public ExtractedWatermark(String location, WatermarkInfo watermarkInfo) {
            this.location = location;
            this.watermarkInfo = watermarkInfo;
        }
        
        // Getters
        public String getLocation() { return location; }
        public WatermarkInfo getWatermarkInfo() { return watermarkInfo; }
    }

    /**
     * 水印信息
     */
    public static class WatermarkInfo {
        private String userId;
        private String ipAddress;
        private long timestamp;
        private String sessionId;

        public static WatermarkInfoBuilder builder() {
            return new WatermarkInfoBuilder();
        }

        // Getters
        public String getUserId() { return userId; }
        public String getIpAddress() { return ipAddress; }
        public long getTimestamp() { return timestamp; }
        public String getSessionId() { return sessionId; }

        public static class WatermarkInfoBuilder {
            private WatermarkInfo info = new WatermarkInfo();
            
            public WatermarkInfoBuilder userId(String userId) {
                info.userId = userId;
                return this;
            }
            
            public WatermarkInfoBuilder ipAddress(String ipAddress) {
                info.ipAddress = ipAddress;
                return this;
            }
            
            public WatermarkInfoBuilder timestamp(long timestamp) {
                info.timestamp = timestamp;
                return this;
            }
            
            public WatermarkInfoBuilder sessionId(String sessionId) {
                info.sessionId = sessionId;
                return this;
            }
            
            public WatermarkInfo build() {
                return info;
            }
        }
    }

    /**
     * 溯源报告
     */
    public static class TraceReport {
        private long extractTime;
        private int watermarkCount;
        private Map<String, Integer> userStatistics;
        private Map<String, Integer> ipStatistics;
        private int uniqueSessionCount;
        private List<ExtractedWatermark> watermarksDetails;

        // Getters and Setters
        public long getExtractTime() { return extractTime; }
        public void setExtractTime(long extractTime) { this.extractTime = extractTime; }
        
        public int getWatermarkCount() { return watermarkCount; }
        public void setWatermarkCount(int watermarkCount) { this.watermarkCount = watermarkCount; }
        
        public Map<String, Integer> getUserStatistics() { return userStatistics; }
        public void setUserStatistics(Map<String, Integer> userStatistics) { this.userStatistics = userStatistics; }
        
        public Map<String, Integer> getIpStatistics() { return ipStatistics; }
        public void setIpStatistics(Map<String, Integer> ipStatistics) { this.ipStatistics = ipStatistics; }
        
        public int getUniqueSessionCount() { return uniqueSessionCount; }
        public void setUniqueSessionCount(int uniqueSessionCount) { this.uniqueSessionCount = uniqueSessionCount; }
        
        public List<ExtractedWatermark> getWatermarksDetails() { return watermarksDetails; }
        public void setWatermarksDetails(List<ExtractedWatermark> watermarksDetails) { this.watermarksDetails = watermarksDetails; }
    }
}
