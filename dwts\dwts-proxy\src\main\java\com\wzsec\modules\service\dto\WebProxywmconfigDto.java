package com.wzsec.modules.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@Data
public class WebProxywmconfigDto implements Serializable {

    /**
     * ID
     */
    private Long id;

    /**
     * 代理配置ID(名称)
     */
    private String proxyconfigid;

    /**
     * 水印信息(自定义_请求IP_时间yyyyddmm)
     */
    private String webwatermark;

    /**
     * 水印颜色
     */
    private String wmcolor;

    /**
     * 水印高度(单个)
     */
    private String wmheight;

    /**
     * 水印宽度(单个)
     */
    private String wmwidth;

    /**
     * 水印字体大小
     */
    private String wmfontsize;

    /**
     * 水印倾斜度
     */
    private String wmangle;

    /**
     * 水印状态(0启用,1禁用)
     */
    private String wmbstate;

    /**
     * 备注
     */
    private String note;

    /**
     * 创建用户名
     */
    private String createuser;

    /**
     * 创建时间
     */
    private Timestamp createtime;

    /**
     * 更新用户
     */
    private String updateuser;

    /**
     * 更新时间
     */
    private Timestamp updatetime;

    /**
     * 备用字段1
     */
    private String sparefield1;

    /**
     * 备用字段2
     */
    private String sparefield2;

    /**
     * 备用字段3
     */
    private String sparefield3;

    /**
     * 备用字段4
     */
    private String sparefield4;

    /**
     * 备用字段5
     */
    private String sparefield5;
}