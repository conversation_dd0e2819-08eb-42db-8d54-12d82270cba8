package com.wzsec.modules.repository;

import com.wzsec.modules.domain.WebProxyconfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
public interface WebProxyconfigRepository extends JpaRepository<WebProxyconfig, Integer>, JpaSpecificationExecutor<WebProxyconfig> {

    /**
     * 根据名称查询主键
     *
     * @return int
     */
    @Query(value = "SELECT * FROM dwts_web_proxyconfig WHERE executionengine = ?1 AND isvalid='0' AND proxystate='1'", nativeQuery = true)
    List<WebProxyconfig> findWebProxyConfigByEngine(String executionEngine);


    /**
     * 更新状态
     *
     * @param id
     */
    @Modifying
    @Transactional
    @Query(value = "update dwts_web_proxyconfig set proxystate = '1' where id =?1", nativeQuery = true)
    void updateStatus(Long id);

    /**
     * 根据代理端口和状态查找配置
     *
     * @param proxyPort 代理端口
     * @param isValid   是否有效（0启用，1禁用）
     * @return 代理配置
     */
    WebProxyconfig findByProxyportAndIsvalid(String proxyPort, String isValid);

    /**
     * 根据代理端口查找有效配置
     *
     * @param proxyPort 代理端口
     * @return 代理配置列表
     */
    @Query(value = "SELECT * FROM dwts_web_proxyconfig WHERE proxyport = ?1 AND isvalid = '0'", nativeQuery = true)
    List<WebProxyconfig> findActiveConfigsByPort(String proxyPort);
}