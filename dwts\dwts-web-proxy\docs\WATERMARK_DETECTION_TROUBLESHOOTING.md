# 水印检测问题排查指南

## 问题描述

从日志中可以看到系统发现了零宽字符序列，但是在解码时出现"二进制长度不是8的倍数"的警告，导致无法正确解码水印信息。

## 常见问题及解决方案

### 1. 二进制长度不是8的倍数

**问题现象：**
```
WARN c.wzsec.webproxy.watermark.util.WatermarkExtractor - 二进制长度 2 不是8的倍数，可能解码不完整
```

**原因分析：**
- 水印编码时每个字符转换为8位二进制，然后每2位转换为一个零宽字符
- 解码时需要收集到完整的零宽字符序列才能正确解码
- 如果只收集到部分零宽字符，会导致二进制长度不足

**解决方案：**
1. 检查水印嵌入是否完整
2. 确认内容传输过程中零宽字符没有丢失
3. 验证编码和解码算法的一致性

### 2. 零宽字符序列不完整

**问题现象：**
```
INFO c.wzsec.webproxy.watermark.util.WatermarkExtractor - 总共发现 11 个零宽字符序列
INFO c.wzsec.webproxy.watermark.util.WatermarkExtractor - 从内容中提取到 0 个水印
```

**原因分析：**
- 发现了零宽字符但无法解码成有效的水印信息
- 可能是零宽字符被分散在不同位置，没有形成完整序列

**解决方案：**
1. 使用改进的序列查找算法
2. 收集所有零宽字符进行统一解码
3. 增强容错能力

### 3. 编码解码不匹配

**问题现象：**
- 能发现零宽字符但解码失败
- 解码后的内容无法解析为有效的水印信息

**原因分析：**
- 编码和解码使用的映射表不一致
- 字符编码格式不匹配

**解决方案：**
确保编码和解码使用相同的映射表：
```java
// 编码映射（InvisibleWatermarkProcessor）
ENCODING_MAP.put("00", ZERO_WIDTH_SPACE);           // \u200B
ENCODING_MAP.put("01", ZERO_WIDTH_NON_JOINER);      // \u200C
ENCODING_MAP.put("10", ZERO_WIDTH_JOINER);          // \u200D
ENCODING_MAP.put("11", WORD_JOINER);                // \u2060

// 解码映射（WatermarkExtractor）
DECODING_MAP.put(ZERO_WIDTH_SPACE, "00");           // \u200B
DECODING_MAP.put(ZERO_WIDTH_NON_JOINER, "01");      // \u200C
DECODING_MAP.put(ZERO_WIDTH_JOINER, "10");          // \u200D
DECODING_MAP.put(WORD_JOINER, "11");                // \u2060
```

## 修复内容

### 2025-08-06 修复

**问题描述：**
- 系统发现零宽字符序列但二进制长度只有2位，无法正常解码
- 零宽字符序列被错误分割，导致解码失败

**修复内容：**

1. **改进序列查找算法**
   - 增加多种序列查找策略：分隔符包围、连续字符块、全局收集
   - 支持处理分散的零宽字符

2. **增强解码容错能力**
   - 对不足8位的二进制序列进行补齐或截断处理
   - 过滤控制字符，提高解码成功率
   - 增加多种解码尝试策略

3. **改进水印数据解析**
   - 支持多种分隔符格式
   - 增加数据清理和验证
   - 容错处理校验和验证失败的情况

4. **增加调试和测试工具**
   - 新增 `WatermarkTestController` 用于测试和调试
   - 改进 `WatermarkDebugUtil` 的解码逻辑
   - 提供详细的日志输出

**修复后的改进：**
- 能够处理分散的零宽字符序列
- 支持不完整序列的补齐和截断
- 提供多种解码策略的容错机制
- 增强了调试和分析能力

## 调试步骤

### 1. 检查服务状态

```bash
curl -X GET "http://localhost:9090/api/watermark/health"
```

### 2. 使用新的测试接口

```bash
# 创建测试内容
curl -X GET "http://localhost:9090/api/watermark/test/create-test-content"

# 测试解码功能
curl -X POST "http://localhost:9090/api/watermark/test/decode" \
  -H "Content-Type: application/json" \
  -d '{"content":"包含零宽字符的内容","contentType":"application/json"}'

# 分析现有内容
curl -X POST "http://localhost:9090/api/watermark/test/analyze" \
  -H "Content-Type: application/json" \
  -d '{"content":"要分析的内容"}'
```

### 3. 测试简单内容

```bash
# 测试纯文本
curl -X POST "http://localhost:9090/api/watermark/detect" \
  -H "Content-Type: text/plain" \
  -d "Hello World"

# 测试JSON
curl -X POST "http://localhost:9090/api/watermark/detect" \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```

### 3. 查看详细日志

在 `application.yml` 中设置日志级别：
```yaml
logging:
  level:
    com.wzsec.webproxy.watermark: DEBUG
```

### 4. 验证零宽字符

使用以下工具检查内容中的零宽字符：
```javascript
// 在浏览器控制台中运行
function analyzeZeroWidthChars(text) {
    const zeroWidthChars = {
        '\u200B': 'ZERO_WIDTH_SPACE',
        '\u200C': 'ZERO_WIDTH_NON_JOINER', 
        '\u200D': 'ZERO_WIDTH_JOINER',
        '\u2060': 'WORD_JOINER',
        '\u2062': 'INVISIBLE_SEPARATOR'
    };
    
    let found = [];
    for (let i = 0; i < text.length; i++) {
        const char = text[i];
        if (zeroWidthChars[char]) {
            found.push({
                position: i,
                char: char,
                name: zeroWidthChars[char],
                unicode: '\\u' + char.charCodeAt(0).toString(16).toUpperCase().padStart(4, '0')
            });
        }
    }
    
    console.log('发现零宽字符:', found);
    return found;
}

// 使用示例
analyzeZeroWidthChars(yourContent);
```

## 配置优化

### 1. 调整日志级别

```yaml
logging:
  level:
    com.wzsec.webproxy.watermark.util.WatermarkExtractor: DEBUG
    com.wzsec.webproxy.watermark.impl.InvisibleWatermarkProcessor: DEBUG
```

### 2. 水印配置

```yaml
web-proxy:
  watermark:
    invisible:
      enabled: true
      encoding-strength: medium
      embed-density: 0.3
      max-embed-length: 1000
```

### 3. 内容处理配置

```yaml
web-proxy:
  content:
    enable-api-intercept: true
    max-content-size: 10485760  # 10MB
```

## 常见解决方案

### 1. 重启服务

```bash
# 重启 dwts-web-proxy 服务
systemctl restart dwts-web-proxy
```

### 2. 清理缓存

```bash
# 清理应用缓存
rm -rf /tmp/dwts-web-proxy-cache/*
```

### 3. 检查数据库连接

确保数据库连接正常，水印配置能够正确加载。

### 4. 验证网络传输

确保在网络传输过程中零宽字符没有被过滤或转换。

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 完整的错误日志
2. 测试用的内容样本
3. 当前的配置文件
4. 系统环境信息

通过这些信息可以更好地诊断和解决水印检测问题。
