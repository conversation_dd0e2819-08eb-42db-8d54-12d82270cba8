package com.wzsec.modules.service;

import com.wzsec.modules.domain.WebProxywmconfig;
import com.wzsec.modules.service.dto.WebProxywmconfigDto;
import com.wzsec.modules.service.dto.WebProxywmconfigQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
public interface WebProxywmconfigService {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(WebProxywmconfigQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<WebProxywmconfigDto>
     */
    List<WebProxywmconfigDto> queryAll(WebProxywmconfigQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return WebProxywmconfigDto
     */
    WebProxywmconfigDto findById(Long id);

    /**
     * 创建
     *
     * @param resources /
     * @return WebProxywmconfigDto
     */
    WebProxywmconfigDto create(WebProxywmconfig resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(WebProxywmconfig resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Long[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<WebProxywmconfigDto> all, HttpServletResponse response) throws IOException;
}