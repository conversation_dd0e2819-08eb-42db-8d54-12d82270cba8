package com.wzsec.proxy.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

/**
 * 代理请求处理器
 * 负责处理HTTP请求的预处理和后处理
 *
 * <AUTHOR> Team
 * @date 2025/08/06
 */
@Slf4j
@Component
public class ProxyRequestHandler {

    /**
     * 预处理请求
     */
    public void preprocessRequest(HttpServletRequest request) {
        if (log.isDebugEnabled()) {
            logRequestDetails(request);
        }
    }

    /**
     * 构建代理请求头
     */
    public HttpHeaders buildProxyHeaders(HttpServletRequest request, String targetHost) {
        HttpHeaders headers = new HttpHeaders();
        
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            
            // 跳过不应该转发的头
            if (shouldSkipRequestHeader(headerName)) {
                continue;
            }
            
            String headerValue = request.getHeader(headerName);
            
            // 特殊处理某些头
            if ("host".equalsIgnoreCase(headerName)) {
                headers.add(headerName, targetHost);
            } else if ("referer".equalsIgnoreCase(headerName)) {
                // 重写Referer头中的主机信息
                headers.add(headerName, rewriteReferer(headerValue, request, targetHost));
            } else {
                headers.add(headerName, headerValue);
            }
        }
        
        // 添加代理标识头
        headers.add("X-Forwarded-For", getClientIpAddress(request));
        headers.add("X-Forwarded-Proto", request.getScheme());
        headers.add("X-Forwarded-Host", request.getHeader("Host"));
        headers.add("X-DWTS-Proxy", "true");
        
        return headers;
    }

    /**
     * 判断是否应该跳过某个请求头
     */
    private boolean shouldSkipRequestHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.equals("content-length") ||
               lowerName.equals("connection") ||
               lowerName.equals("upgrade") ||
               lowerName.equals("proxy-connection") ||
               lowerName.equals("proxy-authenticate") ||
               lowerName.equals("proxy-authorization") ||
               lowerName.equals("te") ||
               lowerName.equals("trailers") ||
               lowerName.equals("transfer-encoding");
    }

    /**
     * 重写Referer头
     */
    private String rewriteReferer(String referer, HttpServletRequest request, String targetHost) {
        if (referer == null || referer.isEmpty()) {
            return referer;
        }
        
        try {
            // 简单的主机替换
            String currentHost = request.getHeader("Host");
            if (currentHost != null && referer.contains(currentHost)) {
                return referer.replace(currentHost, targetHost);
            }
        } catch (Exception e) {
            log.warn("重写Referer头失败: {}", e.getMessage());
        }
        
        return referer;
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 记录请求详细信息
     */
    private void logRequestDetails(HttpServletRequest request) {
        log.debug("=== 代理请求详情 ===");
        log.debug("方法: {}", request.getMethod());
        log.debug("URI: {}", request.getRequestURI());
        log.debug("查询字符串: {}", request.getQueryString());
        log.debug("协议: {}", request.getProtocol());
        log.debug("服务器端口: {}", request.getServerPort());
        log.debug("客户端IP: {}", getClientIpAddress(request));
        
        log.debug("--- 请求头 ---");
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            log.debug("{}: {}", headerName, headerValue);
        }
        
        log.debug("--- 请求参数 ---");
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String[] paramValues = request.getParameterValues(paramName);
            log.debug("{}: {}", paramName, String.join(", ", paramValues));
        }
    }

    /**
     * 检查是否为静态资源请求
     */
    public boolean isStaticResource(HttpServletRequest request) {
        String path = request.getRequestURI().toLowerCase();
        return path.endsWith(".css") ||
               path.endsWith(".js") ||
               path.endsWith(".png") ||
               path.endsWith(".jpg") ||
               path.endsWith(".jpeg") ||
               path.endsWith(".gif") ||
               path.endsWith(".ico") ||
               path.endsWith(".svg") ||
               path.endsWith(".woff") ||
               path.endsWith(".woff2") ||
               path.endsWith(".ttf") ||
               path.endsWith(".eot") ||
               path.contains("/static/") ||
               path.contains("/assets/") ||
               path.contains("/public/");
    }

    /**
     * 检查是否为API请求
     */
    public boolean isApiRequest(HttpServletRequest request, String apiPathPatterns) {
        if (apiPathPatterns == null || apiPathPatterns.trim().isEmpty()) {
            // 默认API路径模式
            String path = request.getRequestURI();
            return path.contains("/api/") ||
                   path.contains("/rest/") ||
                   path.contains("/service/") ||
                   path.endsWith(".json") ||
                   path.endsWith(".xml");
        }
        
        String[] patterns = apiPathPatterns.split(",");
        String requestPath = request.getRequestURI();
        
        for (String pattern : patterns) {
            pattern = pattern.trim();
            if (pattern.isEmpty()) {
                continue;
            }
            
            // 简单的通配符匹配
            if (matchesPattern(requestPath, pattern)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 简单的路径模式匹配
     */
    private boolean matchesPattern(String path, String pattern) {
        if (pattern.equals("/**")) {
            return true;
        }
        
        if (pattern.endsWith("/**")) {
            String prefix = pattern.substring(0, pattern.length() - 3);
            return path.startsWith(prefix);
        }
        
        if (pattern.startsWith("**/")) {
            String suffix = pattern.substring(3);
            return path.endsWith(suffix);
        }
        
        if (pattern.contains("*")) {
            // 简单的通配符支持
            String regex = pattern.replace("*", ".*");
            return path.matches(regex);
        }
        
        return path.equals(pattern) || path.startsWith(pattern);
    }

    /**
     * 检查是否为页面请求
     */
    public boolean isPageRequest(HttpServletRequest request) {
        String path = request.getRequestURI().toLowerCase();
        String acceptHeader = request.getHeader("Accept");
        
        // 检查Accept头是否包含text/html
        if (acceptHeader != null && acceptHeader.contains("text/html")) {
            return true;
        }
        
        // 检查路径是否像页面请求
        return path.equals("/") ||
               path.endsWith("/") ||
               path.endsWith(".html") ||
               path.endsWith(".htm") ||
               (!path.contains(".") && !isApiRequest(request, null));
    }
}
