package com.wzsec.webproxy.watermark;

import com.wzsec.webproxy.watermark.util.WatermarkExtractor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * 水印解码测试
 * 
 * <AUTHOR>
 * @date 2025/08/06
 */
@Slf4j
@SpringBootTest
public class WatermarkDecodingTest {

    @Test
    public void testWatermarkDecoding() {
        WatermarkExtractor extractor = new WatermarkExtractor();
        
        // 测试包含零宽字符的文本
        String testContent = createTestContentWithZeroWidthChars();
        
        log.info("=== 开始水印解码测试 ===");
        log.info("测试内容长度: {} 字符", testContent.length());
        
        // 提取水印
        List<WatermarkExtractor.ExtractedWatermark> watermarks = 
            extractor.extractWatermarks(testContent, "application/json");
        
        log.info("提取到 {} 个水印", watermarks.size());
        
        for (int i = 0; i < watermarks.size(); i++) {
            WatermarkExtractor.ExtractedWatermark watermark = watermarks.get(i);
            log.info("水印 {}: 类型={}, 位置={}, 内容={}", 
                    i + 1, watermark.getType(), watermark.getPosition(), watermark.getContent());
        }
    }
    
    /**
     * 创建包含零宽字符的测试内容
     */
    private String createTestContentWithZeroWidthChars() {
        // 模拟一个包含零宽字符水印的JSON响应
        StringBuilder content = new StringBuilder();
        
        content.append("{");
        content.append("\"status\": \"success\",");
        
        // 在字符串中嵌入零宽字符（模拟水印）
        content.append("\"message\": \"操作");
        
        // 添加零宽字符序列（模拟水印编码）
        // 这里使用一个简单的测试序列
        content.append('\u2062'); // 分隔符
        content.append('\u200B'); // 00
        content.append('\u200C'); // 01
        content.append('\u200D'); // 10
        content.append('\u2060'); // 11
        content.append('\u200B'); // 00
        content.append('\u200C'); // 01
        content.append('\u200D'); // 10
        content.append('\u2060'); // 11
        content.append('\u2062'); // 分隔符
        
        content.append("成功\",");
        content.append("\"data\": {");
        content.append("\"id\": 123,");
        content.append("\"name\": \"测试数据\"");
        content.append("}");
        content.append("}");
        
        return content.toString();
    }
    
    @Test
    public void testMultipleZeroWidthSequences() {
        WatermarkExtractor extractor = new WatermarkExtractor();
        
        // 创建包含多个零宽字符序列的内容
        StringBuilder content = new StringBuilder();
        content.append("这是一段");
        
        // 第一个序列
        content.append('\u200B'); // 零宽字符
        content.append('\u200C');
        
        content.append("包含多个");
        
        // 第二个序列
        content.append('\u200D');
        content.append('\u2060');
        
        content.append("零宽字符序列的");
        
        // 第三个序列
        content.append('\u200B');
        content.append('\u200C');
        content.append('\u200D');
        
        content.append("测试文本");
        
        log.info("=== 测试多个零宽字符序列 ===");
        log.info("测试内容: {}", content.toString());
        
        List<WatermarkExtractor.ExtractedWatermark> watermarks = 
            extractor.extractWatermarks(content.toString(), "text/plain");
        
        log.info("提取结果: {} 个水印", watermarks.size());
    }
    
    @Test
    public void testIncompleteSequences() {
        WatermarkExtractor extractor = new WatermarkExtractor();
        
        // 创建包含不完整序列的内容
        StringBuilder content = new StringBuilder();
        content.append("测试不完整序列: ");
        
        // 只有2位的序列（应该被处理）
        content.append('\u200B'); // 00
        content.append('\u200C'); // 01
        
        content.append(" 中间文本 ");
        
        // 另一个短序列
        content.append('\u200D'); // 10
        
        content.append(" 结束");
        
        log.info("=== 测试不完整序列处理 ===");
        
        List<WatermarkExtractor.ExtractedWatermark> watermarks = 
            extractor.extractWatermarks(content.toString(), "text/plain");
        
        log.info("不完整序列处理结果: {} 个水印", watermarks.size());
    }
}
