package com.wzsec.modules.service.dto;

import com.wzsec.annotation.Query;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@Data
public class WebProxyrecordQueryCriteria {

    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> requesttime;

    @Query(blurry = "requestip,executingstate,applicationname,webseverip,webwatermark,note")
    private String blurry;
}
