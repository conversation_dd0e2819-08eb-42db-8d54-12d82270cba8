package com.wzsec.webproxy.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 水印调试工具类
 * 用于分析和调试零宽字符水印
 *
 * <AUTHOR>
 * @date 2025/08/06
 */
@Slf4j
@Component
public class WatermarkDebugUtil {

    // 零宽字符定义
    private static final char ZERO_WIDTH_SPACE = '\u200B';           // 零宽空格
    private static final char ZERO_WIDTH_NON_JOINER = '\u200C';      // 零宽非连接符
    private static final char ZERO_WIDTH_JOINER = '\u200D';          // 零宽连接符
    private static final char WORD_JOINER = '\u2060';                // 词连接符
    private static final char INVISIBLE_SEPARATOR = '\u2062';        // 不可见分隔符

    // 字符名称映射
    private static final Map<Character, String> CHAR_NAMES = new HashMap<>();
    static {
        CHAR_NAMES.put(ZERO_WIDTH_SPACE, "ZERO_WIDTH_SPACE(\\u200B)");
        CHAR_NAMES.put(ZERO_WIDTH_NON_JOINER, "ZERO_WIDTH_NON_JOINER(\\u200C)");
        CHAR_NAMES.put(ZERO_WIDTH_JOINER, "ZERO_WIDTH_JOINER(\\u200D)");
        CHAR_NAMES.put(WORD_JOINER, "WORD_JOINER(\\u2060)");
        CHAR_NAMES.put(INVISIBLE_SEPARATOR, "INVISIBLE_SEPARATOR(\\u2062)");
    }

    /**
     * 分析文本中的零宽字符
     */
    public Map<String, Object> analyzeZeroWidthChars(String text) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> charDetails = new ArrayList<>();
        Map<String, Integer> charCounts = new HashMap<>();
        
        log.info("=== 开始分析零宽字符 ===");
        log.info("文本长度: {} 字符", text.length());
        
        int zeroWidthCount = 0;
        int position = 0;
        
        for (char c : text.toCharArray()) {
            if (CHAR_NAMES.containsKey(c)) {
                zeroWidthCount++;
                String charName = CHAR_NAMES.get(c);
                
                Map<String, Object> charDetail = new HashMap<>();
                charDetail.put("position", position);
                charDetail.put("char", String.valueOf(c));
                charDetail.put("unicode", String.format("\\u%04X", (int) c));
                charDetail.put("name", charName);
                charDetails.add(charDetail);
                
                charCounts.merge(charName, 1, Integer::sum);
                
                log.info("位置 {}: {} ({})", position, charName, String.format("\\u%04X", (int) c));
            }
            position++;
        }
        
        log.info("总共发现 {} 个零宽字符", zeroWidthCount);
        log.info("字符统计: {}", charCounts);
        
        result.put("totalLength", text.length());
        result.put("zeroWidthCount", zeroWidthCount);
        result.put("charCounts", charCounts);
        result.put("charDetails", charDetails);
        result.put("hasInvisibleSeparator", charCounts.containsKey("INVISIBLE_SEPARATOR(\\u2062)"));
        
        return result;
    }

    /**
     * 提取零宽字符序列（改进版本）
     */
    public List<String> extractZeroWidthSequences(String text) {
        List<String> sequences = new ArrayList<>();
        StringBuilder currentSequence = new StringBuilder();
        
        log.info("=== 开始提取零宽字符序列 ===");
        
        for (char c : text.toCharArray()) {
            if (CHAR_NAMES.containsKey(c)) {
                currentSequence.append(c);
                log.debug("添加零宽字符: {}", CHAR_NAMES.get(c));
            } else if (currentSequence.length() > 0) {
                // 遇到非零宽字符，结束当前序列
                String sequence = currentSequence.toString();
                sequences.add(sequence);
                log.info("发现零宽字符序列，长度: {} 字符", sequence.length());
                currentSequence.setLength(0);
            }
        }
        
        // 处理末尾的序列
        if (currentSequence.length() > 0) {
            String sequence = currentSequence.toString();
            sequences.add(sequence);
            log.info("发现末尾零宽字符序列，长度: {} 字符", sequence.length());
        }
        
        log.info("总共提取到 {} 个零宽字符序列", sequences.size());
        return sequences;
    }

    /**
     * 尝试解码零宽字符序列（改进版本）
     */
    public List<String> tryDecodeSequences(List<String> sequences) {
        List<String> decodedResults = new ArrayList<>();

        // 解码映射表
        Map<Character, String> decodingMap = new HashMap<>();
        decodingMap.put(ZERO_WIDTH_SPACE, "00");
        decodingMap.put(ZERO_WIDTH_NON_JOINER, "01");
        decodingMap.put(ZERO_WIDTH_JOINER, "10");
        decodingMap.put(WORD_JOINER, "11");

        for (int i = 0; i < sequences.size(); i++) {
            String sequence = sequences.get(i);
            log.info("=== 尝试解码序列 {} ===", i + 1);
            log.info("序列长度: {} 字符", sequence.length());

            try {
                String decodedText = decodeSequenceWithImprovedLogic(sequence, decodingMap, i + 1);
                decodedResults.add(decodedText);

            } catch (Exception e) {
                log.error("解码序列 {} 失败: {}", i + 1, e.getMessage());
                decodedResults.add("解码失败: " + e.getMessage());
            }
        }

        // 如果所有单独序列都解码失败，尝试合并解码
        if (sequences.size() > 1 && decodedResults.stream().allMatch(r -> r.startsWith("解码失败") || r.trim().isEmpty())) {
            log.info("=== 尝试合并所有序列解码 ===");
            try {
                StringBuilder merged = new StringBuilder();
                for (String seq : sequences) {
                    merged.append(seq);
                }
                String mergedDecoded = decodeSequenceWithImprovedLogic(merged.toString(), decodingMap, 0);
                if (!mergedDecoded.trim().isEmpty()) {
                    decodedResults.add("合并解码: " + mergedDecoded);
                }
            } catch (Exception e) {
                log.error("合并解码失败: {}", e.getMessage());
            }
        }

        return decodedResults;
    }

    /**
     * 使用改进逻辑解码序列
     */
    private String decodeSequenceWithImprovedLogic(String sequence, Map<Character, String> decodingMap, int sequenceIndex) {
        StringBuilder binaryBuilder = new StringBuilder();

        // 将零宽字符转换为二进制
        for (char c : sequence.toCharArray()) {
            if (c == INVISIBLE_SEPARATOR) {
                log.debug("跳过分隔符");
                continue;
            }

            String bits = decodingMap.get(c);
            if (bits != null) {
                binaryBuilder.append(bits);
                log.debug("字符 {} -> {}", CHAR_NAMES.get(c), bits);
            } else {
                log.warn("未知的零宽字符: {}", String.format("\\u%04X", (int) c));
            }
        }

        String binaryString = binaryBuilder.toString();
        log.info("二进制字符串: {} (长度: {})", binaryString, binaryString.length());

        if (binaryString.length() < 8) {
            log.warn("二进制序列太短 ({}位)，跳过解码", binaryString.length());
            return "";
        }

        if (binaryString.length() % 8 != 0) {
            log.warn("二进制长度 {} 不是8的倍数，可能解码不完整", binaryString.length());

            // 尝试补齐或截断
            int remainder = binaryString.length() % 8;
            int paddingNeeded = 8 - remainder;

            if (paddingNeeded <= 4) {
                // 补齐
                StringBuilder padding = new StringBuilder();
                for (int j = 0; j < paddingNeeded; j++) {
                    padding.append("0");
                }
                binaryString = binaryString + padding.toString();
                log.info("补齐后的二进制字符串: {} (长度: {})", binaryString, binaryString.length());
            } else {
                // 截断
                int newLength = (binaryString.length() / 8) * 8;
                binaryString = binaryString.substring(0, newLength);
                log.info("截断后的二进制字符串: {} (长度: {})", binaryString, binaryString.length());
            }
        }

        StringBuilder result = new StringBuilder();

        // 每8位二进制转换为一个字符
        for (int j = 0; j < binaryString.length(); j += 8) {
            if (j + 8 <= binaryString.length()) {
                String byteString = binaryString.substring(j, j + 8);
                try {
                    int charCode = Integer.parseInt(byteString, 2);
                    char decodedChar = (char) charCode;

                    // 过滤控制字符
                    if (charCode >= 32 || charCode == 9 || charCode == 10 || charCode == 13) {
                        result.append(decodedChar);
                        log.debug("二进制 {} -> 字符码 {} -> 字符 '{}'", byteString, charCode, decodedChar);
                    } else {
                        log.debug("跳过控制字符: 二进制 {} -> 字符码 {}", byteString, charCode);
                    }
                } catch (NumberFormatException e) {
                    log.warn("无法解析二进制字符串: {}", byteString);
                }
            }
        }

        String decodedText = result.toString();
        log.info("解码结果: '{}'", decodedText);
        return decodedText;
    }

    /**
     * 完整分析文本中的水印
     */
    public Map<String, Object> fullAnalysis(String text) {
        Map<String, Object> result = new HashMap<>();
        
        // 1. 分析零宽字符
        Map<String, Object> charAnalysis = analyzeZeroWidthChars(text);
        result.put("charAnalysis", charAnalysis);
        
        // 2. 提取序列
        List<String> sequences = extractZeroWidthSequences(text);
        result.put("sequences", sequences);
        result.put("sequenceCount", sequences.size());
        
        // 3. 尝试解码
        List<String> decodedResults = tryDecodeSequences(sequences);
        result.put("decodedResults", decodedResults);
        
        // 4. 总结
        boolean hasWatermark = !sequences.isEmpty() && decodedResults.stream().anyMatch(s -> !s.startsWith("解码失败"));
        result.put("hasWatermark", hasWatermark);
        result.put("summary", hasWatermark ? "检测到水印" : "未检测到有效水印");
        
        return result;
    }
}
