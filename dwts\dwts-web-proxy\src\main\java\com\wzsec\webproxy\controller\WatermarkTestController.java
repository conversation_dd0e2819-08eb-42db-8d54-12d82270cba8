package com.wzsec.webproxy.controller;

import com.wzsec.webproxy.util.WatermarkDebugUtil;
import com.wzsec.webproxy.watermark.util.WatermarkExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 水印测试控制器
 * 用于测试和调试水印解码功能
 * 
 * <AUTHOR>
 * @date 2025/08/06
 */
@Slf4j
@RestController
@RequestMapping("/api/watermark/test")
public class WatermarkTestController {

    private final WatermarkExtractor watermarkExtractor = new WatermarkExtractor();
    private final WatermarkDebugUtil debugUtil = new WatermarkDebugUtil();

    /**
     * 测试水印解码
     */
    @PostMapping("/decode")
    public Map<String, Object> testWatermarkDecoding(@RequestBody Map<String, String> request) {
        String content = request.get("content");
        String contentType = request.getOrDefault("contentType", "application/json");
        
        log.info("=== 开始水印解码测试 ===");
        log.info("内容长度: {} 字符", content != null ? content.length() : 0);
        log.info("内容类型: {}", contentType);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 分析零宽字符
            log.info("=== 分析零宽字符 ===");
            debugUtil.analyzeZeroWidthChars(content);
            
            // 2. 提取零宽字符序列
            List<String> sequences = debugUtil.extractZeroWidthSequences(content);
            result.put("sequenceCount", sequences.size());
            
            // 3. 尝试解码序列
            List<String> decodedResults = debugUtil.tryDecodeSequences(sequences);
            result.put("decodedResults", decodedResults);
            
            // 4. 使用正式的水印提取器
            List<WatermarkExtractor.ExtractedWatermark> watermarks = 
                watermarkExtractor.extractWatermarks(content, contentType);
            result.put("watermarkCount", watermarks.size());
            result.put("watermarks", watermarks);
            
            result.put("success", true);
            result.put("message", "解码测试完成");
            
        } catch (Exception e) {
            log.error("水印解码测试失败", e);
            result.put("success", false);
            result.put("message", "解码测试失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 创建测试用的零宽字符内容
     */
    @GetMapping("/create-test-content")
    public Map<String, Object> createTestContent() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 创建包含零宽字符的测试内容
            StringBuilder content = new StringBuilder();
            content.append("{\"status\":\"success\",\"message\":\"测试");
            
            // 添加零宽字符序列（模拟水印）
            content.append('\u2062'); // 分隔符
            
            // 编码 "test|192.168.1.1|1234567890|session123"
            String testData = "test|192.168.1.1|1234567890|sess";
            for (char c : testData.toCharArray()) {
                String binary = String.format("%8s", Integer.toBinaryString(c)).replace(' ', '0');
                
                // 每2位转换为零宽字符
                for (int i = 0; i < binary.length(); i += 2) {
                    String bits = binary.substring(i, Math.min(i + 2, binary.length()));
                    if (bits.length() == 1) bits += "0";
                    
                    switch (bits) {
                        case "00": content.append('\u200B'); break; // 零宽空格
                        case "01": content.append('\u200C'); break; // 零宽非连接符
                        case "10": content.append('\u200D'); break; // 零宽连接符
                        case "11": content.append('\u2060'); break; // 词连接符
                    }
                }
            }
            
            content.append('\u2062'); // 分隔符
            content.append("数据\",\"data\":{\"id\":123}}");
            
            result.put("content", content.toString());
            result.put("length", content.length());
            result.put("description", "包含零宽字符水印的测试内容");
            
            // 分析创建的内容
            log.info("=== 创建的测试内容分析 ===");
            debugUtil.analyzeZeroWidthChars(content.toString());
            
        } catch (Exception e) {
            log.error("创建测试内容失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 分析现有内容中的零宽字符
     */
    @PostMapping("/analyze")
    public Map<String, Object> analyzeContent(@RequestBody Map<String, String> request) {
        String content = request.get("content");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("=== 分析内容中的零宽字符 ===");
            
            // 统计零宽字符
            int zeroWidthCount = 0;
            Map<String, Integer> charCounts = new HashMap<>();
            
            for (char c : content.toCharArray()) {
                String charName = getZeroWidthCharName(c);
                if (charName != null) {
                    zeroWidthCount++;
                    charCounts.put(charName, charCounts.getOrDefault(charName, 0) + 1);
                }
            }
            
            result.put("totalZeroWidthChars", zeroWidthCount);
            result.put("charCounts", charCounts);
            
            // 提取序列
            List<String> sequences = debugUtil.extractZeroWidthSequences(content);
            result.put("sequences", sequences.size());
            
            // 详细分析
            debugUtil.analyzeZeroWidthChars(content);
            
        } catch (Exception e) {
            log.error("分析内容失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取零宽字符名称
     */
    private String getZeroWidthCharName(char c) {
        switch (c) {
            case '\u200B': return "ZERO_WIDTH_SPACE";
            case '\u200C': return "ZERO_WIDTH_NON_JOINER";
            case '\u200D': return "ZERO_WIDTH_JOINER";
            case '\u2060': return "WORD_JOINER";
            case '\u2062': return "INVISIBLE_SEPARATOR";
            default: return null;
        }
    }
}
