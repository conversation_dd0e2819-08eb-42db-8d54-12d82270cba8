package com.wzsec.dotask.mask.webmark;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * Web水印服务
 * 提供Web水印处理的统一接口
 *
 * <AUTHOR> Team
 * @date 2025/08/06
 */
@Slf4j
@Service
public class WebWatermarkService {

    @Autowired
    private WatermarkProcessorFactory processorFactory;

    /**
     * 处理水印加注
     *
     * @param content     原始内容
     * @param contentType 内容类型
     * @param request     HTTP请求
     * @param config      水印配置
     * @return 处理后的内容
     */
    public byte[] processWatermark(byte[] content, String contentType,
                                 HttpServletRequest request, WatermarkConfig config) {
        
        if (content == null || content.length == 0) {
            log.debug("内容为空，跳过水印处理");
            return content;
        }

        if (config == null) {
            log.debug("配置为空，跳过水印处理");
            return content;
        }

        try {
            // 根据内容类型和配置获取合适的处理器
            WatermarkProcessor processor = processorFactory.getProcessor(contentType, config);
            
            if (processor == null) {
                log.debug("未找到合适的水印处理器，内容类型: {}", contentType);
                return content;
            }

            log.info("使用处理器 [{}] 处理水印，内容类型: {}", 
                    processor.getProcessorName(), contentType);

            // 处理水印
            byte[] result = processor.processWatermark(content, contentType, request, config);
            
            log.info("水印处理完成，原始大小: {} bytes, 处理后大小: {} bytes", 
                    content.length, result.length);

            return result;

        } catch (Exception e) {
            log.error("水印处理失败", e);
            return content; // 失败时返回原内容
        }
    }

    /**
     * 提取水印信息
     *
     * @param content     包含水印的内容
     * @param contentType 内容类型
     * @return 提取的水印信息列表
     */
    public List<WatermarkInfo> extractWatermarks(byte[] content, String contentType) {
        List<WatermarkInfo> watermarks = new java.util.ArrayList<>();
        
        if (content == null || content.length == 0) {
            log.debug("内容为空，无法提取水印");
            return watermarks;
        }

        try {
            // 获取所有处理器
            List<WatermarkProcessor> processors = processorFactory.getAllProcessors();
            
            for (WatermarkProcessor processor : processors) {
                if (processor.canHandle(contentType)) {
                    try {
                        WatermarkInfo watermark = processor.extractWatermark(content, contentType);
                        if (watermark != null && watermark.isValid()) {
                            watermarks.add(watermark);
                            log.info("使用处理器 [{}] 提取到水印: {}", 
                                    processor.getProcessorName(), watermark.getWatermarkText());
                        }
                    } catch (Exception e) {
                        log.warn("处理器 [{}] 提取水印失败: {}", 
                                processor.getProcessorName(), e.getMessage());
                    }
                }
            }

            log.info("水印提取完成，共提取到 {} 个水印", watermarks.size());

        } catch (Exception e) {
            log.error("水印提取失败", e);
        }

        return watermarks;
    }

    /**
     * 检查内容是否包含水印
     *
     * @param content     内容
     * @param contentType 内容类型
     * @return 是否包含水印
     */
    public boolean hasWatermark(byte[] content, String contentType) {
        List<WatermarkInfo> watermarks = extractWatermarks(content, contentType);
        return !watermarks.isEmpty();
    }

    /**
     * 获取支持的内容类型
     *
     * @return 支持的内容类型列表
     */
    public List<String> getSupportedContentTypes() {
        List<String> contentTypes = new java.util.ArrayList<>();
        contentTypes.add("text/html");
        contentTypes.add("application/xhtml+xml");
        contentTypes.add("application/json");
        contentTypes.add("application/xml");
        contentTypes.add("text/xml");
        contentTypes.add("text/plain");
        return contentTypes;
    }

    /**
     * 检查是否支持指定的内容类型
     *
     * @param contentType 内容类型
     * @return 是否支持
     */
    public boolean isSupportedContentType(String contentType) {
        if (contentType == null) {
            return false;
        }

        List<WatermarkProcessor> processors = processorFactory.getAllProcessors();
        for (WatermarkProcessor processor : processors) {
            if (processor.canHandle(contentType)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取处理器统计信息
     *
     * @return 统计信息
     */
    public String getProcessorStats() {
        return processorFactory.getProcessorStats();
    }

    /**
     * 创建默认水印配置
     *
     * @return 默认配置
     */
    public WatermarkConfig createDefaultConfig() {
        return WatermarkConfig.getDefault();
    }

    /**
     * 验证水印配置
     *
     * @param config 水印配置
     * @return 验证结果
     */
    public boolean validateConfig(WatermarkConfig config) {
        if (config == null) {
            return false;
        }

        // 检查基本配置
        if (config.getWatermarkText() == null || config.getWatermarkText().trim().isEmpty()) {
            log.warn("水印文本不能为空");
            return false;
        }

        // 检查透明度范围
        if (config.getWatermarkOpacity() != null) {
            double opacity = config.getWatermarkOpacity();
            if (opacity < 0.0 || opacity > 1.0) {
                log.warn("水印透明度必须在0.0-1.0之间，当前值: {}", opacity);
                return false;
            }
        }

        // 检查尺寸
        if (config.getWatermarkWidth() != null && config.getWatermarkWidth() <= 0) {
            log.warn("水印宽度必须大于0，当前值: {}", config.getWatermarkWidth());
            return false;
        }

        if (config.getWatermarkHeight() != null && config.getWatermarkHeight() <= 0) {
            log.warn("水印高度必须大于0，当前值: {}", config.getWatermarkHeight());
            return false;
        }

        // 检查嵌入密度
        if (config.getInvisibleEmbedDensity() != null) {
            double density = config.getInvisibleEmbedDensity();
            if (density < 0.0 || density > 1.0) {
                log.warn("暗水印嵌入密度必须在0.0-1.0之间，当前值: {}", density);
                return false;
            }
        }

        return true;
    }

    /**
     * 健康检查
     *
     * @return 健康状态
     */
    public boolean healthCheck() {
        try {
            List<WatermarkProcessor> processors = processorFactory.getAllProcessors();
            if (processors.isEmpty()) {
                log.error("没有可用的水印处理器");
                return false;
            }

            // 检查每个处理器
            for (WatermarkProcessor processor : processors) {
                if (processor == null) {
                    log.error("发现空的水印处理器");
                    return false;
                }
            }

            log.info("水印服务健康检查通过，共 {} 个处理器可用", processors.size());
            return true;

        } catch (Exception e) {
            log.error("水印服务健康检查失败", e);
            return false;
        }
    }
}
