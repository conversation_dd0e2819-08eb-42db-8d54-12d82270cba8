package com.wzsec.modules.service.impl;

import com.wzsec.modules.domain.WebProxywmconfig;
import com.wzsec.modules.repository.WebProxywmconfigRepository;
import com.wzsec.modules.service.WebProxywmconfigService;
import com.wzsec.modules.service.dto.WebProxywmconfigDto;
import com.wzsec.modules.service.dto.WebProxywmconfigQueryCriteria;
import com.wzsec.modules.service.mapper.WebProxywmconfigMapper;
import com.wzsec.utils.FileUtil;
import com.wzsec.utils.PageUtil;
import com.wzsec.utils.QueryHelp;
import com.wzsec.utils.ValidationUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@Service
//@CacheConfig(cacheNames = "webProxywmconfig")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class WebProxywmconfigServiceImpl implements WebProxywmconfigService {

    private final WebProxywmconfigRepository webProxywmconfigRepository;

    private final WebProxywmconfigMapper webProxywmconfigMapper;

    public WebProxywmconfigServiceImpl(WebProxywmconfigRepository webProxywmconfigRepository, WebProxywmconfigMapper webProxywmconfigMapper) {
        this.webProxywmconfigRepository = webProxywmconfigRepository;
        this.webProxywmconfigMapper = webProxywmconfigMapper;
    }

    @Override
    //@Cacheable
    public Map<String, Object> queryAll(WebProxywmconfigQueryCriteria criteria, Pageable pageable) {
        Page<WebProxywmconfig> page = webProxywmconfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(webProxywmconfigMapper::toDto));
    }

    @Override
    //@Cacheable
    public List<WebProxywmconfigDto> queryAll(WebProxywmconfigQueryCriteria criteria) {
        return webProxywmconfigMapper.toDto(webProxywmconfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    //@Cacheable(key = "#p0")
    public WebProxywmconfigDto findById(Long id) {
        WebProxywmconfig webProxywmconfig = webProxywmconfigRepository.findById(id).orElseGet(WebProxywmconfig::new);
        ValidationUtil.isNull(webProxywmconfig.getId(), "WebProxywmconfig", "id", id);
        return webProxywmconfigMapper.toDto(webProxywmconfig);
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public WebProxywmconfigDto create(WebProxywmconfig resources) {
        return webProxywmconfigMapper.toDto(webProxywmconfigRepository.save(resources));
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(WebProxywmconfig resources) {
        WebProxywmconfig webProxywmconfig = webProxywmconfigRepository.findById(resources.getId()).orElseGet(WebProxywmconfig::new);
        ValidationUtil.isNull(webProxywmconfig.getId(), "WebProxywmconfig", "id", resources.getId());
        webProxywmconfig.copy(resources);
        webProxywmconfigRepository.save(webProxywmconfig);
    }

    @Override
    //@CacheEvict(allEntries = true)
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            webProxywmconfigRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<WebProxywmconfigDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (WebProxywmconfigDto webProxywmconfig : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("代理配置ID(名称)", webProxywmconfig.getProxyconfigid());
            map.put("水印信息(自定义_请求IP_时间yyyyddmm)", webProxywmconfig.getWebwatermark());
            map.put("水印颜色", webProxywmconfig.getWmcolor());
            map.put("水印高度(单个)", webProxywmconfig.getWmheight());
            map.put("水印宽度(单个)", webProxywmconfig.getWmwidth());
            map.put("水印字体大小", webProxywmconfig.getWmfontsize());
            map.put("水印倾斜度", webProxywmconfig.getWmangle());
            map.put("水印状态(0启用,1禁用)", webProxywmconfig.getWmbstate());
            map.put("备注", webProxywmconfig.getNote());
            map.put("创建用户名", webProxywmconfig.getCreateuser());
            map.put("创建时间", webProxywmconfig.getCreatetime());
            map.put("更新用户", webProxywmconfig.getUpdateuser());
            map.put("更新时间", webProxywmconfig.getUpdatetime());
            map.put("备用字段1", webProxywmconfig.getSparefield1());
            map.put("备用字段2", webProxywmconfig.getSparefield2());
            map.put("备用字段3", webProxywmconfig.getSparefield3());
            map.put("备用字段4", webProxywmconfig.getSparefield4());
            map.put("备用字段5", webProxywmconfig.getSparefield5());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}