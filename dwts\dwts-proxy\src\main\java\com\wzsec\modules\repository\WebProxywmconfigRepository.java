package com.wzsec.modules.repository;

import com.wzsec.modules.domain.WebProxywmconfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
public interface WebProxywmconfigRepository extends JpaRepository<WebProxywmconfig, Long>, JpaSpecificationExecutor<WebProxywmconfig> {

    /**
     * 根据代理配置ID获取水印配置信息
     *
     * @return int
     */
    @Query(value = "SELECT * FROM dwts_web_proxywmconfig WHERE proxyconfigid = ?1 AND wmbstate ='0' LIMIT 1", nativeQuery = true)
    WebProxywmconfig queryWebWatermarkConfig(String proxyconfigid);


}