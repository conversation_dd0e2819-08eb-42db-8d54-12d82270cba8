package com.wzsec.dotask.mask.webmark;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;

/**
 * 抽象水印处理器
 * 提供水印处理的通用方法和工具
 *
 * <AUTHOR> Team
 * @date 2025/08/06
 */
@Slf4j
public abstract class AbstractWatermarkProcessor implements WatermarkProcessor {

    // 常用的内容类型模式
    protected static final Pattern HTML_PATTERN = Pattern.compile("text/html", Pattern.CASE_INSENSITIVE);
    protected static final Pattern JSON_PATTERN = Pattern.compile("application/json", Pattern.CASE_INSENSITIVE);
    protected static final Pattern XML_PATTERN = Pattern.compile("(application|text)/xml", Pattern.CASE_INSENSITIVE);
    protected static final Pattern TEXT_PATTERN = Pattern.compile("text/plain", Pattern.CASE_INSENSITIVE);

    /**
     * 安全地将字节数组转换为字符串
     */
    protected String safeToString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        return new String(bytes, StandardCharsets.UTF_8);
    }

    /**
     * 安全地将字符串转换为字节数组
     */
    protected byte[] safeToBytes(String str) {
        if (str == null) {
            return new byte[0];
        }
        return str.getBytes(StandardCharsets.UTF_8);
    }

    /**
     * 检查是否为HTML内容
     */
    protected boolean isHtmlContent(String contentType) {
        return contentType != null && HTML_PATTERN.matcher(contentType).find();
    }

    /**
     * 检查是否为JSON内容
     */
    protected boolean isJsonContent(String contentType) {
        return contentType != null && JSON_PATTERN.matcher(contentType).find();
    }

    /**
     * 检查是否为XML内容
     */
    protected boolean isXmlContent(String contentType) {
        return contentType != null && XML_PATTERN.matcher(contentType).find();
    }

    /**
     * 检查是否为文本内容
     */
    protected boolean isTextContent(String contentType) {
        return contentType != null && TEXT_PATTERN.matcher(contentType).find();
    }

    /**
     * 生成水印文本
     */
    protected String generateWatermarkText(HttpServletRequest request, WatermarkConfig config) {
        String template = config.getWatermarkText();
        if (template == null || template.trim().isEmpty()) {
            template = "DWTS_{IP}_{DATE}";
        }

        // 获取客户端IP
        String clientIp = getClientIpAddress(request);

        // 获取当前时间
        String currentDate = String.valueOf(System.currentTimeMillis() / 1000);

        // 替换模板变量
        String watermarkText = template
                .replace("{IP}", clientIp)
                .replace("{DATE}", currentDate)
                .replace("{TIME}", currentDate)
                .replace("{USER}", request.getRemoteUser() != null ? request.getRemoteUser() : "anonymous")
                .replace("{SESSION}", request.getSession(false) != null ? request.getSession().getId() : "nosession")
                .replace("{APP}", config.getApplicationName() != null ? config.getApplicationName() : "unknown");

        return watermarkText;
    }

    /**
     * 获取客户端真实IP地址
     */
    protected String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 创建水印信息对象
     */
    protected WatermarkInfo createWatermarkInfo(HttpServletRequest request, WatermarkConfig config, String watermarkText) {
        WatermarkInfo info = new WatermarkInfo();
        info.setWatermarkText(watermarkText);
        info.setUserIp(getClientIpAddress(request));
        info.setTimestamp(System.currentTimeMillis());
        info.setApplicationName(config.getApplicationName());
        info.setRequestPath(request.getRequestURI());
        info.setRequestMethod(request.getMethod());
        info.setUserAgent(request.getHeader("User-Agent"));
        info.setReferer(request.getHeader("Referer"));
        info.setSessionId(request.getSession(false) != null ? request.getSession().getId() : null);
        return info;
    }

    /**
     * 格式化内容用于日志输出
     */
    protected String formatContentForLog(String content) {
        if (content == null) {
            return "null";
        }
        
        if (content.length() <= 200) {
            return content;
        }
        
        return content.substring(0, 100) + "..." + content.substring(content.length() - 100);
    }

    /**
     * 验证JSON结构完整性
     */
    protected void verifyJsonStructure(String original, String processed) {
        try {
            // 简单验证：检查大括号和方括号是否匹配
            if (countChar(original, '{') != countChar(processed, '{') ||
                countChar(original, '}') != countChar(processed, '}') ||
                countChar(original, '[') != countChar(processed, '[') ||
                countChar(original, ']') != countChar(processed, ']')) {
                
                log.warn("JSON结构可能被破坏，括号数量不匹配");
            }
        } catch (Exception e) {
            log.warn("JSON结构验证失败: {}", e.getMessage());
        }
    }

    /**
     * 验证XML结构完整性
     */
    protected void verifyXmlStructure(String original, String processed) {
        try {
            // 简单验证：检查尖括号是否匹配
            if (countChar(original, '<') != countChar(processed, '<') ||
                countChar(original, '>') != countChar(processed, '>')) {
                
                log.warn("XML结构可能被破坏，标签数量不匹配");
            }
        } catch (Exception e) {
            log.warn("XML结构验证失败: {}", e.getMessage());
        }
    }

    /**
     * 计算字符出现次数
     */
    private int countChar(String str, char ch) {
        int count = 0;
        for (char c : str.toCharArray()) {
            if (c == ch) {
                count++;
            }
        }
        return count;
    }

    /**
     * 检查内容是否为空
     */
    protected boolean isEmpty(String content) {
        return content == null || content.trim().isEmpty();
    }

    /**
     * 检查内容是否为空
     */
    protected boolean isEmpty(byte[] content) {
        return content == null || content.length == 0;
    }

    /**
     * 获取安全的配置值
     */
    protected String getSafeConfigValue(String value, String defaultValue) {
        return value != null && !value.trim().isEmpty() ? value : defaultValue;
    }

    /**
     * 获取安全的配置值
     */
    protected Double getSafeConfigValue(Double value, Double defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * 获取安全的配置值
     */
    protected Integer getSafeConfigValue(Integer value, Integer defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * 获取安全的配置值
     */
    protected Boolean getSafeConfigValue(Boolean value, Boolean defaultValue) {
        return value != null ? value : defaultValue;
    }
}
