package com.wzsec.modules.repository;

import com.wzsec.modules.domain.WebProxyconfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
public interface WebProxyconfigRepository extends JpaRepository<WebProxyconfig, Integer>, JpaSpecificationExecutor<WebProxyconfig> {

    /**
     * 根据名称查询主键
     *
     * @return int
     */
    @Query(value = "SELECT * FROM dwts_web_proxyconfig WHERE executionengine = ?1 AND isvalid='0' AND proxystate='1'", nativeQuery = true)
    List<WebProxyconfig> findWebProxyConfigByEngine(String executionEngine);


    /**
     * 更新状态
     *
     * @param id
     */
    @Modifying
    @Transactional
    @Query(value = "update dwts_web_proxyconfig set proxystate = '1' where id =?1", nativeQuery = true)
    void updateStatus(Long id);
}