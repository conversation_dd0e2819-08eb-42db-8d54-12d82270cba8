package com.wzsec.dotask.mask.webmark;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 水印信息类
 * 封装水印的详细信息
 *
 * <AUTHOR> Team
 * @date 2025/08/06
 */
@Data
public class WatermarkInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 水印ID */
    private String watermarkId;

    /** 水印文本 */
    private String watermarkText;

    /** 水印类型 (PAGE/API/INVISIBLE) */
    private String watermarkType;

    /** 用户IP */
    private String userIp;

    /** 用户ID */
    private String userId;

    /** 时间戳 */
    private Long timestamp;

    /** 会话ID */
    private String sessionId;

    /** 应用名称 */
    private String applicationName;

    /** 请求路径 */
    private String requestPath;

    /** 请求方法 */
    private String requestMethod;

    /** 用户代理 */
    private String userAgent;

    /** 引用页面 */
    private String referer;

    /** 扩展属性 */
    private Map<String, Object> properties;

    /** 水印强度/可信度 */
    private Double confidence;

    /** 水印位置信息 */
    private String position;

    /** 编码方式 */
    private String encoding;

    /**
     * 创建页面水印信息
     */
    public static WatermarkInfo createPageWatermark(String watermarkText, String userIp) {
        WatermarkInfo info = new WatermarkInfo();
        info.setWatermarkText(watermarkText);
        info.setWatermarkType("PAGE");
        info.setUserIp(userIp);
        info.setTimestamp(System.currentTimeMillis());
        info.setConfidence(1.0);
        info.setEncoding("SVG");
        return info;
    }

    /**
     * 创建API水印信息
     */
    public static WatermarkInfo createApiWatermark(String watermarkText, String userIp) {
        WatermarkInfo info = new WatermarkInfo();
        info.setWatermarkText(watermarkText);
        info.setWatermarkType("API");
        info.setUserIp(userIp);
        info.setTimestamp(System.currentTimeMillis());
        info.setConfidence(0.8);
        info.setEncoding("ZERO_WIDTH");
        return info;
    }

    /**
     * 创建不可见水印信息
     */
    public static WatermarkInfo createInvisibleWatermark(String watermarkText, String userIp) {
        WatermarkInfo info = new WatermarkInfo();
        info.setWatermarkText(watermarkText);
        info.setWatermarkType("INVISIBLE");
        info.setUserIp(userIp);
        info.setTimestamp(System.currentTimeMillis());
        info.setConfidence(0.9);
        info.setEncoding("ZERO_WIDTH_CHARS");
        return info;
    }

    /**
     * 生成水印ID
     */
    public String generateWatermarkId() {
        if (watermarkId == null) {
            watermarkId = String.format("%s_%s_%d", 
                watermarkType != null ? watermarkType : "UNKNOWN",
                userIp != null ? userIp.replace(".", "_") : "UNKNOWN",
                timestamp != null ? timestamp : System.currentTimeMillis());
        }
        return watermarkId;
    }

    /**
     * 检查水印是否有效
     */
    public boolean isValid() {
        return watermarkText != null && 
               !watermarkText.trim().isEmpty() &&
               watermarkType != null &&
               timestamp != null &&
               confidence != null &&
               confidence > 0;
    }

    /**
     * 获取格式化的时间戳
     */
    public String getFormattedTimestamp() {
        if (timestamp == null) {
            return null;
        }
        return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                .format(new java.util.Date(timestamp));
    }

    /**
     * 添加扩展属性
     */
    public void addProperty(String key, Object value) {
        if (properties == null) {
            properties = new java.util.HashMap<>();
        }
        properties.put(key, value);
    }

    /**
     * 获取扩展属性
     */
    public Object getProperty(String key) {
        return properties != null ? properties.get(key) : null;
    }

    /**
     * 获取扩展属性（带默认值）
     */
    public Object getProperty(String key, Object defaultValue) {
        Object value = getProperty(key);
        return value != null ? value : defaultValue;
    }
}
