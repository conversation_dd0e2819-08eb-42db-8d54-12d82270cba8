package com.wzsec.modules.service.impl;

import com.wzsec.modules.domain.WebProxyconfig;
import com.wzsec.modules.repository.WebProxyconfigRepository;
import com.wzsec.modules.service.WebProxyconfigService;
import com.wzsec.modules.service.dto.WebProxyconfigDto;
import com.wzsec.modules.service.dto.WebProxyconfigQueryCriteria;
import com.wzsec.modules.service.mapper.WebProxyconfigMapper;
import com.wzsec.utils.PageUtil;
import com.wzsec.utils.QueryHelp;
import com.wzsec.utils.ValidationUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@Service
//@CacheConfig(cacheNames = "webProxyconfig")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class WebProxyconfigServiceImpl implements WebProxyconfigService {

    //脚本的地址
    @Value("${refresh.scriptPath}")
    private String scriptPath;

    private final WebProxyconfigRepository webProxyconfigRepository;

    private final WebProxyconfigMapper webProxyconfigMapper;

    public WebProxyconfigServiceImpl(WebProxyconfigRepository webProxyconfigRepository, WebProxyconfigMapper webProxyconfigMapper) {
        this.webProxyconfigRepository = webProxyconfigRepository;
        this.webProxyconfigMapper = webProxyconfigMapper;
    }

    @Override
    //@Cacheable
    public Map<String, Object> queryAll(WebProxyconfigQueryCriteria criteria, Pageable pageable) {
        Page<WebProxyconfig> page = webProxyconfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(webProxyconfigMapper::toDto));
    }

    @Override
    //@Cacheable
    public List<WebProxyconfigDto> queryAll(WebProxyconfigQueryCriteria criteria) {
        return webProxyconfigMapper.toDto(webProxyconfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    //@Cacheable(key = "#p0")
    public WebProxyconfigDto findById(Integer id) {
        WebProxyconfig webProxyconfig = webProxyconfigRepository.findById(id).orElseGet(WebProxyconfig::new);
        ValidationUtil.isNull(webProxyconfig.getId(), "WebProxyconfig", "id", id);
        return webProxyconfigMapper.toDto(webProxyconfig);
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public WebProxyconfigDto create(WebProxyconfig resources) {
        return webProxyconfigMapper.toDto(webProxyconfigRepository.save(resources));
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(WebProxyconfig resources) {
        WebProxyconfig webProxyconfig = webProxyconfigRepository.findById(resources.getId()).orElseGet(WebProxyconfig::new);
        ValidationUtil.isNull(webProxyconfig.getId(), "WebProxyconfig", "id", resources.getId());
        webProxyconfig.copy(resources);
        webProxyconfigRepository.save(webProxyconfig);
    }

    @Override
    //@CacheEvict(allEntries = true)
    public void deleteAll(Integer[] ids) {
        for (Integer id : ids) {
            webProxyconfigRepository.deleteById(id);
        }
    }

    @Override
    public boolean test(Long id) {
        if (StringUtils.isNotEmpty(scriptPath)) {
            //根据ID更新状态为可用
            webProxyconfigRepository.updateStatus(id);
            String osName = System.getProperty("os.name").toLowerCase();
            if (!osName.contains("win")) {
                //检测可用即进行重启操作
                ProcessBuilder sh = new ProcessBuilder("sh", scriptPath, "restart");
                try {
                    proxyServerOperating(null, sh);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return true;
        }else{
            return false;
        }
    }

    /**
     * 代理服务器操作
     *
     * @param file 文件
     * @param pb   pb
     * @throws IOException ioexception
     */
    public static void proxyServerOperating(File file, ProcessBuilder pb) throws IOException {
        // 不使用Runtime.getRuntime().exec(command)的方式,因为无法设置以下特性
        // Java执行本地命令是启用一个子进程处理,默认情况下子进程与父进程I/O通过管道相连(默认ProcessBuilder.Redirect.PIPE)
        // 当服务执行自身重启的命令时,父进程关闭导致管道连接中断,将导致子进程也崩溃,从而无法完成后续的启动
        // 解决方式,(1)设置子进程IO输出重定向到指定文件;(2)设置属性子进程的I/O源或目标将与当前进程的相同,两者相互独立
        if (file == null || !file.exists()) {
            // 设置属性子进程的I/O源或目标将与当前进程的相同,两者相互独立
            pb.redirectOutput(ProcessBuilder.Redirect.INHERIT);
            pb.redirectError(ProcessBuilder.Redirect.INHERIT);
            pb.redirectInput(ProcessBuilder.Redirect.INHERIT);
        } else {
            // 设置子进程IO输出重定向到指定文件
            // 错误输出与标准输出,输出到一块
            pb.redirectErrorStream(true);
            // 设置输出日志
            pb.redirectOutput(ProcessBuilder.Redirect.appendTo(file));
        }
        // 执行命令进程
        System.out.println(pb.command());
        pb.start();
    }


}