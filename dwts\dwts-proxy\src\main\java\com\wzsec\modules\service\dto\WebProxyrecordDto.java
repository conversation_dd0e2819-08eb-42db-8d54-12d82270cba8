package com.wzsec.modules.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@Data
public class WebProxyrecordDto implements Serializable {

    /**
     * ID
     */
    private Long id;

    /**
     * 请求IP
     */
    private String requestip;

    /**
     * 请求时间
     */
    private Timestamp requesttime;

    /**
     * 执行状态(成功,失败)
     */
    private String executingstate;

    /**
     * web应用名称
     */
    private String applicationname;

    /**
     * web应用IP
     */
    private String webseverip;

    /**
     * 水印信息
     */
    private String webwatermark;

    /**
     * 备注
     */
    private String note;

    /**
     * 插入时间
     */
    private Timestamp createtime;
}