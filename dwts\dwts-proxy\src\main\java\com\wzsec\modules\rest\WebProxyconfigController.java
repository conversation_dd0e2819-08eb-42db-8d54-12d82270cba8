package com.wzsec.modules.rest;

import com.wzsec.annotation.AnonymousAccess;
import com.wzsec.modules.service.WebProxyconfigService;
import com.wzsec.aop.log.Log;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@RestController
@RequestMapping("/engine/proxyConfig")
public class WebProxyconfigController {

    private final WebProxyconfigService webProxyconfigService;

    public WebProxyconfigController(WebProxyconfigService webProxyconfigService) {
        this.webProxyconfigService = webProxyconfigService;
    }

    @Log("测试执行引擎状态并重启")
    @AnonymousAccess
    @PostMapping(value = "/test/{id}")
    public ResponseEntity<Object> test(@PathVariable Long id) {
        return new ResponseEntity<>(webProxyconfigService.test(id), HttpStatus.OK);
    }
}
