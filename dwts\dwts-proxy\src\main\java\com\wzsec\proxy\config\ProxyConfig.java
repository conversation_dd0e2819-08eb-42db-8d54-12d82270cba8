package com.wzsec.proxy.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.*;
import java.security.cert.X509Certificate;

/**
 * 代理配置类
 * 配置RestTemplate和其他代理相关的Bean
 *
 * <AUTHOR> Team
 * @date 2025/08/06
 */
@Slf4j
@Configuration
public class ProxyConfig {

    /**
     * 配置RestTemplate用于代理请求
     */
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        
        // 设置请求工厂
        ClientHttpRequestFactory factory = createRequestFactory();
        restTemplate.setRequestFactory(factory);
        
        log.info("RestTemplate配置完成，支持HTTP和HTTPS代理");
        
        return restTemplate;
    }

    /**
     * 创建请求工厂
     */
    private ClientHttpRequestFactory createRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        
        // 设置连接超时时间（毫秒）
        factory.setConnectTimeout(30000);
        
        // 设置读取超时时间（毫秒）
        factory.setReadTimeout(60000);
        
        // 设置缓冲请求体
        factory.setBufferRequestBody(false);
        
        return factory;
    }

    /**
     * 配置信任所有SSL证书的RestTemplate（用于HTTPS代理）
     * 注意：这在生产环境中可能存在安全风险，建议根据实际需求配置
     */
    @Bean("httpsRestTemplate")
    public RestTemplate httpsRestTemplate() {
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    
                    @Override
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        // 信任所有客户端证书
                    }
                    
                    @Override
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        // 信任所有服务器证书
                    }
                }
            };

            // 创建SSL上下文
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

            // 设置默认的SSL套接字工厂
            HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
            
            // 设置主机名验证器
            HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    return true; // 信任所有主机名
                }
            });

            RestTemplate restTemplate = new RestTemplate();
            ClientHttpRequestFactory factory = createRequestFactory();
            restTemplate.setRequestFactory(factory);
            
            log.info("HTTPS RestTemplate配置完成，已禁用SSL证书验证");
            
            return restTemplate;
            
        } catch (Exception e) {
            log.error("配置HTTPS RestTemplate失败", e);
            // 如果配置失败，返回普通的RestTemplate
            return restTemplate();
        }
    }
}
