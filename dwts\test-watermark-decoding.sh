#!/bin/bash

# 水印解码测试脚本
# 用于测试修复后的水印解码功能

echo "=== 水印解码功能测试 ==="

# 服务器地址
SERVER_URL="http://localhost:9090"

# 测试1: 创建测试内容
echo "1. 创建测试内容..."
curl -s -X GET "${SERVER_URL}/api/watermark/test/create-test-content" | jq '.'

echo ""
echo "2. 测试水印解码..."

# 创建包含零宽字符的测试JSON
TEST_CONTENT='{
  "content": "{\"status\":\"success\",\"message\":\"测试\u2062\u200B\u200C\u200D\u2060\u200B\u200C\u200D\u2060\u2062数据\",\"data\":{\"id\":123}}",
  "contentType": "application/json"
}'

# 发送解码测试请求
curl -s -X POST "${SERVER_URL}/api/watermark/test/decode" \
  -H "Content-Type: application/json" \
  -d "$TEST_CONTENT" | jq '.'

echo ""
echo "3. 分析现有内容..."

# 分析内容
ANALYZE_CONTENT='{
  "content": "这是一段包含\u200B\u200C零宽字符\u200D\u2060的测试文本"
}'

curl -s -X POST "${SERVER_URL}/api/watermark/test/analyze" \
  -H "Content-Type: application/json" \
  -d "$ANALYZE_CONTENT" | jq '.'

echo ""
echo "=== 测试完成 ==="
