package com.wzsec.modules.service.impl;

import com.wzsec.modules.domain.WebProxyrecord;
import com.wzsec.modules.repository.WebProxyrecordRepository;
import com.wzsec.modules.service.WebProxyrecordService;
import com.wzsec.modules.service.dto.WebProxyrecordDto;
import com.wzsec.modules.service.dto.WebProxyrecordQueryCriteria;
import com.wzsec.modules.service.mapper.WebProxyrecordMapper;
import com.wzsec.utils.FileUtil;
import com.wzsec.utils.PageUtil;
import com.wzsec.utils.QueryHelp;
import com.wzsec.utils.ValidationUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@Service
//@CacheConfig(cacheNames = "webProxyrecord")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class WebProxyrecordServiceImpl implements WebProxyrecordService {

    private final WebProxyrecordRepository webProxyrecordRepository;

    private final WebProxyrecordMapper webProxyrecordMapper;

    public WebProxyrecordServiceImpl(WebProxyrecordRepository webProxyrecordRepository, WebProxyrecordMapper webProxyrecordMapper) {
        this.webProxyrecordRepository = webProxyrecordRepository;
        this.webProxyrecordMapper = webProxyrecordMapper;
    }

    @Override
    //@Cacheable
    public Map<String, Object> queryAll(WebProxyrecordQueryCriteria criteria, Pageable pageable) {
        Page<WebProxyrecord> page = webProxyrecordRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(webProxyrecordMapper::toDto));
    }

    @Override
    //@Cacheable
    public List<WebProxyrecordDto> queryAll(WebProxyrecordQueryCriteria criteria) {
        return webProxyrecordMapper.toDto(webProxyrecordRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    //@Cacheable(key = "#p0")
    public WebProxyrecordDto findById(Long id) {
        WebProxyrecord webProxyrecord = webProxyrecordRepository.findById(id).orElseGet(WebProxyrecord::new);
        ValidationUtil.isNull(webProxyrecord.getId(), "WebProxyrecord", "id", id);
        return webProxyrecordMapper.toDto(webProxyrecord);
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public WebProxyrecordDto create(WebProxyrecord resources) {
        return webProxyrecordMapper.toDto(webProxyrecordRepository.save(resources));
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(WebProxyrecord resources) {
        WebProxyrecord webProxyrecord = webProxyrecordRepository.findById(resources.getId()).orElseGet(WebProxyrecord::new);
        ValidationUtil.isNull(webProxyrecord.getId(), "WebProxyrecord", "id", resources.getId());
        webProxyrecord.copy(resources);
        webProxyrecordRepository.save(webProxyrecord);
    }

    @Override
    //@CacheEvict(allEntries = true)
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            webProxyrecordRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<WebProxyrecordDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (WebProxyrecordDto webProxyrecord : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("请求IP", webProxyrecord.getRequestip());
            map.put("请求时间", webProxyrecord.getRequesttime());
            map.put("执行状态(成功,失败)", webProxyrecord.getExecutingstate());
            map.put("web应用名称", webProxyrecord.getApplicationname());
            map.put("web应用IP", webProxyrecord.getWebseverip());
            map.put("水印信息", webProxyrecord.getWebwatermark());
            map.put("备注", webProxyrecord.getNote());
            map.put("插入时间", webProxyrecord.getCreatetime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}