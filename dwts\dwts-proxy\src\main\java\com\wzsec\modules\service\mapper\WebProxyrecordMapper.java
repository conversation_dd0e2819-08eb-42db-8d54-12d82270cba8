package com.wzsec.modules.service.mapper;

import com.wzsec.base.BaseMapper;
import com.wzsec.modules.domain.WebProxyrecord;
import com.wzsec.modules.service.dto.WebProxyrecordDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WebProxyrecordMapper extends BaseMapper<WebProxyrecordDto, WebProxyrecord> {

}