package com.wzsec.modules.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@Data
public class WebProxyconfigDto implements Serializable {

    /**
     * ID
     */
    private Integer id;

    /**
     * web应用服务名称
     */
    private String applicationname;

    /**
     * 代理服务地址
     */
    private String executionengine;

    /**
     * 代理端口
     */
    private String proxyport;

    /**
     * web应用服务IP
     */
    private String remoteaddr;

    /**
     * web应用服务端口
     */
    private String remoteport;

    /**
     * 代理服务开关(0启用，1禁用)
     */
    private String isvalid;

    /**
     * 代理服务状态
     */
    private String proxystate;

    /**
     * HTTP协议(HTTP,HTTPS)
     */
    private String httpprotocol;

    /**
     * 备注
     */
    private String note;

    /**
     * 创建用户名
     */
    private String createuser;

    /**
     * 创建时间
     */
    private Timestamp createtime;

    /**
     * 更新用户名
     */
    private String updateuser;

    /**
     * 更新时间
     */
    private Timestamp updatetime;

    /**
     * 备用字段1
     */
    private String sparefield1;

    /**
     * 备用字段2
     */
    private String sparefield2;

    /**
     * 备用字段3
     */
    private String sparefield3;

    /**
     * 备用字段4
     */
    private String sparefield4;

    /**
     * 备用字段5
     */
    private String sparefield5;
}