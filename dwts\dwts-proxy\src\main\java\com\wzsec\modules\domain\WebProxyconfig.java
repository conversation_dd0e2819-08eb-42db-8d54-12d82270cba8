package com.wzsec.modules.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@Entity
@Data
@Table(name = "dwts_web_proxyconfig")
public class WebProxyconfig implements Serializable {

    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /**
     * web应用服务名称
     */
    @Column(name = "applicationname")
    private String applicationname;

    /**
     * 代理服务地址
     */
    @Column(name = "executionengine")
    private String executionengine;

    /**
     * 代理端口
     */
    @Column(name = "proxyport")
    private String proxyport;

    /**
     * web应用服务IP
     */
    @Column(name = "remoteaddr")
    private String remoteaddr;

    /**
     * web应用服务端口
     */
    @Column(name = "remoteport")
    private String remoteport;

    /**
     * 代理服务开关(0启用，1禁用)
     */
    @Column(name = "isvalid")
    private String isvalid;

    /**
     * 代理服务状态
     */
    @Column(name = "proxystate")
    private String proxystate;

    /**
     * HTTP协议(HTTP,HTTPS)
     */
    @Column(name = "httpprotocol")
    private String httpprotocol;

    /**
     * 备注
     */
    @Column(name = "note")
    private String note;

    /**
     * 创建用户名
     */
    @Column(name = "createuser")
    private String createuser;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新用户名
     */
    @Column(name = "updateuser")
    private String updateuser;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /**
     * 备用字段1
     */
    @Column(name = "sparefield1")
    private String sparefield1;

    /**
     * 备用字段2
     */
    @Column(name = "sparefield2")
    private String sparefield2;

    /**
     * 备用字段3
     */
    @Column(name = "sparefield3")
    private String sparefield3;

    /**
     * 备用字段4
     */
    @Column(name = "sparefield4")
    private String sparefield4;

    /**
     * 备用字段5
     */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(WebProxyconfig source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }
}