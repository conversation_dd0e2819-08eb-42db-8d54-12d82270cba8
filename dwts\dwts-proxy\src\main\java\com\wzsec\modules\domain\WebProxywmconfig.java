package com.wzsec.modules.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@Entity
@Data
@Table(name = "dwts_web_proxywmconfig")
public class WebProxywmconfig implements Serializable {

    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 代理配置ID(名称)
     */
    @Column(name = "proxyconfigid")
    private String proxyconfigid;

    /**
     * 水印信息(自定义_请求IP_时间yyyyddmm)
     */
    @Column(name = "webwatermark")
    private String webwatermark;

    /**
     * 水印颜色
     */
    @Column(name = "wmcolor")
    private String wmcolor;

    /**
     * 水印高度(单个)
     */
    @Column(name = "wmheight")
    private String wmheight;

    /**
     * 水印宽度(单个)
     */
    @Column(name = "wmwidth")
    private String wmwidth;

    /**
     * 水印字体大小
     */
    @Column(name = "wmfontsize")
    private String wmfontsize;

    /**
     * 水印倾斜度
     */
    @Column(name = "wmangle")
    private String wmangle;

    /**
     * 水印状态(0启用,1禁用)
     */
    @Column(name = "wmbstate")
    private String wmbstate;

    /**
     * 备注
     */
    @Column(name = "note")
    private String note;

    /**
     * 创建用户名
     */
    @Column(name = "createuser")
    private String createuser;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新用户
     */
    @Column(name = "updateuser")
    private String updateuser;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /**
     * 备用字段1
     */
    @Column(name = "sparefield1")
    private String sparefield1;

    /**
     * 备用字段2
     */
    @Column(name = "sparefield2")
    private String sparefield2;

    /**
     * 备用字段3
     */
    @Column(name = "sparefield3")
    private String sparefield3;

    /**
     * 备用字段4
     */
    @Column(name = "sparefield4")
    private String sparefield4;

    /**
     * 备用字段5
     */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(WebProxywmconfig source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }
}