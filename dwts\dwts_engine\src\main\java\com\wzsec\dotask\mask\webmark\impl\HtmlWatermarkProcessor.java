package com.wzsec.dotask.mask.webmark.impl;

import com.wzsec.dotask.mask.webmark.AbstractWatermarkProcessor;
import com.wzsec.dotask.mask.webmark.WatermarkConfig;
import com.wzsec.dotask.mask.webmark.WatermarkInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.regex.Pattern;

/**
 * HTML水印处理器
 * 负责在HTML页面中注入可见水印
 *
 * <AUTHOR> Team
 * @date 2025/08/06
 */
@Slf4j
@Component
public class HtmlWatermarkProcessor extends AbstractWatermarkProcessor {

    private static final Pattern HEAD_PATTERN = Pattern.compile("</head>", Pattern.CASE_INSENSITIVE);
    private static final Pattern BODY_PATTERN = Pattern.compile("</body>", Pattern.CASE_INSENSITIVE);
    private static final Pattern HTML_PATTERN = Pattern.compile("<html[^>]*>", Pattern.CASE_INSENSITIVE);

    @Override
    public String getProcessorName() {
        return "HTML水印处理器";
    }

    @Override
    public String getWatermarkType() {
        return "PAGE";
    }

    @Override
    public boolean canHandle(String contentType) {
        return isHtmlContent(contentType);
    }

    @Override
    public byte[] processWatermark(byte[] content, String contentType,
                                 HttpServletRequest request, WatermarkConfig config) {
        try {
            log.info("开始处理HTML水印 - ContentType: {}, EnablePageWatermark: {}",
                    contentType, config.isPageWatermarkEnabled());

            if (!config.isPageWatermarkEnabled()) {
                log.info("页面水印已禁用，跳过处理");
                return content;
            }

            String html = safeToString(content);
            if (isEmpty(html)) {
                log.warn("HTML内容为空，跳过水印处理");
                return content;
            }

            log.info("HTML内容长度: {}, 前100字符: {}",
                    html.length(),
                    html.length() > 100 ? html.substring(0, 100) : html);

            // 生成水印文本
            String watermarkText = generateWatermarkText(request, config);
            log.info("生成水印文本: {}", watermarkText);

            // 注入水印
            String watermarkedHtml = injectWatermark(html, watermarkText, request, config);

            log.info("水印注入完成，处理后长度: {}", watermarkedHtml.length());

            return safeToBytes(watermarkedHtml);

        } catch (Exception e) {
            log.error("HTML水印处理失败", e);
            return content; // 失败时返回原内容
        }
    }

    @Override
    public WatermarkInfo extractWatermark(byte[] content, String contentType) {
        // HTML水印提取逻辑（简化实现）
        try {
            String html = safeToString(content);
            
            // 查找水印相关的CSS类或元素
            if (html.contains("dwts-watermark")) {
                WatermarkInfo info = new WatermarkInfo();
                info.setWatermarkType("PAGE");
                info.setEncoding("SVG");
                info.setConfidence(0.9);
                info.setPosition("OVERLAY");
                return info;
            }
            
            return null;
        } catch (Exception e) {
            log.error("HTML水印提取失败", e);
            return null;
        }
    }

    /**
     * 注入水印到HTML中
     */
    private String injectWatermark(String html, String watermarkText, 
                                 HttpServletRequest request, WatermarkConfig config) {
        
        // 1. 注入水印样式
        html = injectWatermarkStyle(html, watermarkText, config);
        
        // 2. 注入水印脚本
        html = injectWatermarkScript(html, config);
        
        // 3. 重写页面链接（如果启用）
        if (getSafeConfigValue(config.getEnableLinkRewrite(), false)) {
            html = rewritePageLinks(html, request, config);
        }
        
        // 4. 注入API拦截脚本（如果启用）
        if (getSafeConfigValue(config.getEnableApiIntercept(), false)) {
            html = injectApiInterceptor(html, request, config);
        }
        
        return html;
    }

    /**
     * 注入水印样式
     */
    private String injectWatermarkStyle(String html, String watermarkText, WatermarkConfig config) {
        // 获取配置参数
        Double opacity = getSafeConfigValue(config.getWatermarkOpacity(), 0.3);
        String color = getSafeConfigValue(config.getWatermarkColor(), "#999999");
        Double angle = getSafeConfigValue(config.getWatermarkAngle(), -30.0);
        Integer width = getSafeConfigValue(config.getWatermarkWidth(), 300);
        Integer height = getSafeConfigValue(config.getWatermarkHeight(), 150);

        // 生成SVG水印
        String svgBase64 = generateWatermarkSvg(watermarkText, color, angle, width, height);

        String watermarkStyle = String.format(
            "<style type=\"text/css\">\n" +
            ".dwts-watermark {\n" +
            "    position: fixed;\n" +
            "    top: 0;\n" +
            "    left: 0;\n" +
            "    width: 100%%;\n" +
            "    height: 100%%;\n" +
            "    pointer-events: none;\n" +
            "    z-index: 9999;\n" +
            "    background-image: url('data:image/svg+xml;base64,%s');\n" +
            "    background-repeat: repeat;\n" +
            "    opacity: %.2f;\n" +
            "}\n" +
            "</style>",
            svgBase64, opacity
        );

        // 在</head>前注入样式
        if (HEAD_PATTERN.matcher(html).find()) {
            return HEAD_PATTERN.matcher(html).replaceFirst(watermarkStyle + "\n</head>");
        } else if (HTML_PATTERN.matcher(html).find()) {
            // 如果没有head标签，在html标签后添加
            return HTML_PATTERN.matcher(html).replaceFirst("$0\n<head>" + watermarkStyle + "</head>");
        } else {
            // 如果都没有，在开头添加
            return watermarkStyle + "\n" + html;
        }
    }

    /**
     * 注入水印脚本
     */
    private String injectWatermarkScript(String html, WatermarkConfig config) {
        String watermarkScript = 
            "<script type=\"text/javascript\">\n" +
            "(function() {\n" +
            "    function createWatermark() {\n" +
            "        if (document.querySelector('.dwts-watermark')) return;\n" +
            "        \n" +
            "        var watermark = document.createElement('div');\n" +
            "        watermark.className = 'dwts-watermark';\n" +
            "        watermark.id = 'dwts-watermark-' + Date.now();\n" +
            "        \n" +
            "        // 防止被删除\n" +
            "        Object.defineProperty(watermark, 'remove', {\n" +
            "            value: function() { createWatermark(); },\n" +
            "            writable: false\n" +
            "        });\n" +
            "        \n" +
            "        document.body.appendChild(watermark);\n" +
            "        \n" +
            "        // 监控水印元素\n" +
            "        if (window.MutationObserver) {\n" +
            "            var observer = new MutationObserver(function(mutations) {\n" +
            "                mutations.forEach(function(mutation) {\n" +
            "                    if (mutation.type === 'childList') {\n" +
            "                        mutation.removedNodes.forEach(function(node) {\n" +
            "                            if (node.className && node.className.indexOf('dwts-watermark') !== -1) {\n" +
            "                                setTimeout(createWatermark, 100);\n" +
            "                            }\n" +
            "                        });\n" +
            "                    }\n" +
            "                });\n" +
            "            });\n" +
            "            \n" +
            "            observer.observe(document.body || document.documentElement, {\n" +
            "                childList: true,\n" +
            "                subtree: true\n" +
            "            });\n" +
            "        }\n" +
            "    }\n" +
            "    \n" +
            "    function initWatermark() {\n" +
            "        createWatermark();\n" +
            "        \n" +
            "        // 定期检查\n" +
            "        setInterval(function() {\n" +
            "            if (!document.querySelector('.dwts-watermark')) {\n" +
            "                createWatermark();\n" +
            "            }\n" +
            "        }, 5000);\n" +
            "    }\n" +
            "    \n" +
            "    // 等待DOM加载完成\n" +
            "    if (document.readyState === 'loading') {\n" +
            "        document.addEventListener('DOMContentLoaded', initWatermark);\n" +
            "    } else {\n" +
            "        initWatermark();\n" +
            "    }\n" +
            "})();\n" +
            "</script>";
        
        // 在</body>前注入脚本
        if (BODY_PATTERN.matcher(html).find()) {
            return BODY_PATTERN.matcher(html).replaceFirst(watermarkScript + "\n</body>");
        } else {
            // 如果没有body标签，在末尾添加
            return html + "\n" + watermarkScript;
        }
    }

    /**
     * 重写页面链接
     */
    private String rewritePageLinks(String html, HttpServletRequest request, WatermarkConfig config) {
        // 简化实现：重写相对链接为绝对链接
        try {
            String currentHost = request.getHeader("Host");
            String targetHost = config.getTargetHost();
            
            if (currentHost != null && targetHost != null && !currentHost.equals(targetHost)) {
                // 重写href和src属性中的链接
                html = html.replaceAll("href=\"/([^\"]*)", "href=\"/" + "$1");
                html = html.replaceAll("src=\"/([^\"]*)", "src=\"/" + "$1");
            }
        } catch (Exception e) {
            log.warn("重写页面链接失败: {}", e.getMessage());
        }
        
        return html;
    }

    /**
     * 注入API拦截脚本
     */
    private String injectApiInterceptor(String html, HttpServletRequest request, WatermarkConfig config) {
        String interceptorScript = 
            "<script type=\"text/javascript\">\n" +
            "(function() {\n" +
            "    // 拦截XMLHttpRequest\n" +
            "    var originalXHR = window.XMLHttpRequest;\n" +
            "    window.XMLHttpRequest = function() {\n" +
            "        var xhr = new originalXHR();\n" +
            "        xhr.setRequestHeader('X-DWTS-Proxy', 'true');\n" +
            "        return xhr;\n" +
            "    };\n" +
            "    \n" +
            "    // 拦截fetch\n" +
            "    if (window.fetch) {\n" +
            "        var originalFetch = window.fetch;\n" +
            "        window.fetch = function(url, options) {\n" +
            "            options = options || {};\n" +
            "            options.headers = options.headers || {};\n" +
            "            options.headers['X-DWTS-Proxy'] = 'true';\n" +
            "            return originalFetch(url, options);\n" +
            "        };\n" +
            "    }\n" +
            "})();\n" +
            "</script>";
        
        return injectWatermarkScript(html + interceptorScript, config);
    }

    /**
     * 生成水印SVG
     */
    private String generateWatermarkSvg(String text, String color, Double angle, Integer width, Integer height) {
        String svgContent = String.format(
            "<svg xmlns='http://www.w3.org/2000/svg' width='%d' height='%d' viewBox='0 0 %d %d'>" +
            "<text x='50%%' y='50%%' font-family='Arial, sans-serif' font-size='16' " +
            "fill='%s' text-anchor='middle' dominant-baseline='middle' " +
            "transform='rotate(%.1f %d %d)'>%s</text>" +
            "</svg>",
            width, height, width, height, color, angle, width/2, height/2, text);

        return Base64.getEncoder().encodeToString(svgContent.getBytes(StandardCharsets.UTF_8));
    }
}
