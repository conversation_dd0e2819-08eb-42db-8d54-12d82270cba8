package com.wzsec.modules.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024-09-20
 */
@Entity
@Data
@Table(name = "dwts_web_proxyconfig")
public class WebProxyconfig implements Serializable {

    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /**
     * web应用服务名称
     */
    @Column(name = "applicationname")
    private String applicationname;

    /**
     * 代理服务地址
     */
    @Column(name = "executionengine")
    private String executionengine;

    /**
     * 代理端口
     */
    @Column(name = "proxyport")
    private String proxyport;

    /**
     * web应用服务IP
     */
    @Column(name = "remoteaddr")
    private String remoteaddr;

    /**
     * web应用服务端口
     */
    @Column(name = "remoteport")
    private String remoteport;

    /**
     * 代理服务开关(0启用，1禁用)
     */
    @Column(name = "isvalid")
    private String isvalid;

    /**
     * 代理服务状态
     */
    @Column(name = "proxystate")
    private String proxystate;

    /**
     * HTTP协议(HTTP,HTTPS)
     */
    @Column(name = "httpprotocol")
    private String httpprotocol;

    /**
     * 备注
     */
    @Column(name = "note")
    private String note;

    /**
     * 创建用户名
     */
    @Column(name = "createuser")
    private String createuser;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新用户名
     */
    @Column(name = "updateuser")
    private String updateuser;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /**
     * 备用字段1
     */
    @Column(name = "sparefield1")
    private String sparefield1;

    /**
     * 备用字段2
     */
    @Column(name = "sparefield2")
    private String sparefield2;

    /**
     * 备用字段3
     */
    @Column(name = "sparefield3")
    private String sparefield3;

    /**
     * 备用字段4
     */
    @Column(name = "sparefield4")
    private String sparefield4;

    /**
     * 备用字段5
     */
    @Column(name = "sparefield5")
    private String sparefield5;

    // ========== 新增水印相关字段 ==========

    /**
     * 水印文本模板
     */
    @Column(name = "watermark_text")
    private String watermarkText;

    /**
     * 是否启用页面水印
     */
    @Column(name = "enable_page_watermark")
    private Boolean enablePageWatermark;

    /**
     * 是否启用API水印（暗水印）
     */
    @Column(name = "enable_api_watermark")
    private Boolean enableApiWatermark;

    /**
     * API路径模式 (逗号分隔)
     */
    @Column(name = "api_path_patterns")
    private String apiPathPatterns;

    /**
     * 水印透明度 (0.0-1.0)
     */
    @Column(name = "watermark_opacity")
    private Double watermarkOpacity;

    /**
     * 水印宽度
     */
    @Column(name = "watermark_width")
    private Integer watermarkWidth;

    /**
     * 水印高度
     */
    @Column(name = "watermark_height")
    private Integer watermarkHeight;

    /**
     * 水印颜色
     */
    @Column(name = "watermark_color")
    private String watermarkColor;

    /**
     * 水印角度
     */
    @Column(name = "watermark_angle")
    private Double watermarkAngle;

    /**
     * 是否启用链接重写
     */
    @Column(name = "enable_link_rewrite")
    private Boolean enableLinkRewrite;

    /**
     * 是否启用API拦截
     */
    @Column(name = "enable_api_intercept")
    private Boolean enableApiIntercept;

    /**
     * 暗水印编码强度
     */
    @Column(name = "invisible_encoding_strength")
    private String invisibleEncodingStrength;

    /**
     * 暗水印嵌入密度
     */
    @Column(name = "invisible_embed_density")
    private Double invisibleEmbedDensity;

    // ========== 业务方法 ==========

    /**
     * 获取目标基础URL
     */
    public String getTargetBaseUrl() {
        if (remoteaddr == null || remoteaddr.trim().isEmpty()) {
            throw new IllegalStateException("目标主机地址不能为空");
        }

        String protocol = httpprotocol != null ? httpprotocol.toLowerCase() : "http";
        Integer port = remoteport != null ? Integer.parseInt(remoteport) : ("https".equals(protocol) ? 443 : 80);

        StringBuilder url = new StringBuilder();
        url.append(protocol).append("://").append(remoteaddr.trim());

        // 只有非标准端口才添加端口号
        if (!"http".equals(protocol) || port != 80) {
            if (!"https".equals(protocol) || port != 443) {
                url.append(":").append(port);
            }
        }

        return url.toString();
    }

    /**
     * 检查配置是否有效
     */
    public boolean isValid() {
        return "0".equals(isvalid) &&  // 0启用，1禁用
                proxyport != null &&
                remoteaddr != null &&
                remoteport != null;
    }

    /**
     * 获取代理端口（整型）
     */
    public Integer getProxyPortInt() {
        return proxyport != null ? Integer.parseInt(proxyport) : null;
    }

    /**
     * 获取目标端口（整型）
     */
    public Integer getTargetPortInt() {
        return remoteport != null ? Integer.parseInt(remoteport) : null;
    }

    /**
     * 获取目标主机
     */
    public String getTargetHost() {
        return remoteaddr;
    }

    /**
     * 获取目标协议
     */
    public String getTargetProtocol() {
        return httpprotocol;
    }

    public void copy(WebProxyconfig source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }
}