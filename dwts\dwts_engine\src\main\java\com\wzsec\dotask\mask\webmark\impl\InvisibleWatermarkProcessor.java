package com.wzsec.dotask.mask.webmark.impl;

import com.wzsec.dotask.mask.webmark.AbstractWatermarkProcessor;
import com.wzsec.dotask.mask.webmark.WatermarkConfig;
import com.wzsec.dotask.mask.webmark.WatermarkInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 不可见水印处理器
 * 使用零宽字符技术在API响应中嵌入不可见水印，支持溯源追踪
 *
 * <AUTHOR> Team
 * @date 2025/08/06
 */
@Slf4j
@Component
public class InvisibleWatermarkProcessor extends AbstractWatermarkProcessor {

    // 零宽字符定义
    private static final char ZERO_WIDTH_SPACE = '\u200B';           // 零宽空格
    private static final char ZERO_WIDTH_NON_JOINER = '\u200C';      // 零宽非连接符
    private static final char ZERO_WIDTH_JOINER = '\u200D';          // 零宽连接符
    private static final char WORD_JOINER = '\u2060';                // 词连接符
    private static final char INVISIBLE_SEPARATOR = '\u2062';        // 不可见分隔符

    // 编码映射表
    private static final Map<String, Character> ENCODING_MAP = new HashMap<>();
    private static final Map<Character, String> DECODING_MAP = new HashMap<>();

    static {
        ENCODING_MAP.put("00", ZERO_WIDTH_SPACE);
        ENCODING_MAP.put("01", ZERO_WIDTH_NON_JOINER);
        ENCODING_MAP.put("10", ZERO_WIDTH_JOINER);
        ENCODING_MAP.put("11", WORD_JOINER);

        // 构建解码映射
        for (Map.Entry<String, Character> entry : ENCODING_MAP.entrySet()) {
            DECODING_MAP.put(entry.getValue(), entry.getKey());
        }
    }

    @Override
    public String getProcessorName() {
        return "不可见水印处理器";
    }

    @Override
    public String getWatermarkType() {
        return "INVISIBLE";
    }

    @Override
    public boolean canHandle(String contentType) {
        return isJsonContent(contentType) || 
               isXmlContent(contentType) || 
               isTextContent(contentType);
    }

    @Override
    public byte[] processWatermark(byte[] content, String contentType,
                                 HttpServletRequest request, WatermarkConfig config) {
        try {
            // 检查是否启用API水印（暗水印）
            if (!config.isApiWatermarkEnabled()) {
                return content;
            }

            String contentString = safeToString(content);
            if (isEmpty(contentString)) {
                return content;
            }

            log.info("=== API水印（暗水印）加注前 ===");
            log.info("内容类型: {}", contentType);
            log.info("原始内容长度: {} bytes", contentString.length());
            log.info("原始内容: {}", formatContentForLog(contentString));

            // 生成水印信息
            WatermarkInfo watermarkInfo = createWatermarkInfo(request, config, 
                    generateWatermarkText(request, config));
            watermarkInfo.setWatermarkType("INVISIBLE");

            // 根据内容类型处理
            String watermarkedContent;
            if (isJsonContent(contentType)) {
                watermarkedContent = processJsonContent(contentString, watermarkInfo);
            } else if (isXmlContent(contentType)) {
                watermarkedContent = processXmlContent(contentString, watermarkInfo);
            } else {
                watermarkedContent = processTextContent(contentString, watermarkInfo);
            }

            // 打印加注后的结果
            log.info("=== API水印（暗水印）加注后 ===");
            log.info("处理后内容长度: {} bytes", watermarkedContent.length());
            log.info("长度变化: {} bytes", watermarkedContent.length() - contentString.length());
            log.info("处理后内容: {}", formatContentForLog(watermarkedContent));

            // 验证数据结构完整性
            if (isJsonContent(contentType)) {
                verifyJsonStructure(contentString, watermarkedContent);
            } else if (isXmlContent(contentType)) {
                verifyXmlStructure(contentString, watermarkedContent);
            }

            log.info("API水印（暗水印）加注完成 - 数据结构保持完整");

            return safeToBytes(watermarkedContent);

        } catch (Exception e) {
            log.error("暗水印处理失败", e);
            return content; // 失败时返回原内容
        }
    }

    @Override
    public WatermarkInfo extractWatermark(byte[] content, String contentType) {
        try {
            String contentString = safeToString(content);
            
            // 提取零宽字符序列
            List<String> sequences = extractZeroWidthSequences(contentString);
            
            if (sequences.isEmpty()) {
                return null;
            }

            // 尝试解码序列
            for (String sequence : sequences) {
                try {
                    String decoded = decodeFromZeroWidthChars(sequence);
                    if (decoded != null && !decoded.isEmpty()) {
                        WatermarkInfo info = parseWatermarkInfo(decoded);
                        if (info != null) {
                            info.setWatermarkType("INVISIBLE");
                            info.setEncoding("ZERO_WIDTH_CHARS");
                            info.setConfidence(0.8);
                            return info;
                        }
                    }
                } catch (Exception e) {
                    log.debug("解码序列失败: {}", e.getMessage());
                }
            }

            return null;
        } catch (Exception e) {
            log.error("不可见水印提取失败", e);
            return null;
        }
    }

    /**
     * 处理JSON内容
     */
    private String processJsonContent(String jsonString, WatermarkInfo watermarkInfo) throws Exception {
        // 在JSON字符串值中嵌入不可见水印
        return injectInvisibleWatermarkToJson(jsonString, watermarkInfo);
    }

    /**
     * 处理XML内容
     */
    private String processXmlContent(String xmlString, WatermarkInfo watermarkInfo) throws Exception {
        // 在XML文本节点中嵌入不可见水印
        return injectInvisibleWatermarkToXml(xmlString, watermarkInfo);
    }

    /**
     * 处理文本内容
     */
    private String processTextContent(String textString, WatermarkInfo watermarkInfo) throws Exception {
        // 在文本中嵌入不可见水印
        return injectInvisibleWatermarkToText(textString, watermarkInfo);
    }

    /**
     * 向JSON中注入不可见水印
     */
    private String injectInvisibleWatermarkToJson(String jsonString, WatermarkInfo watermarkInfo) {
        try {
            // 生成水印编码
            String watermarkCode = encodeWatermarkInfo(watermarkInfo);
            String invisibleWatermark = encodeToZeroWidthChars(watermarkCode);

            // 在第一个字符串值中嵌入水印
            return jsonString.replaceFirst("\"([^\"]+)\"", "\"$1" + invisibleWatermark + "\"");
            
        } catch (Exception e) {
            log.warn("JSON水印注入失败: {}", e.getMessage());
            return jsonString;
        }
    }

    /**
     * 向XML中注入不可见水印
     */
    private String injectInvisibleWatermarkToXml(String xmlString, WatermarkInfo watermarkInfo) {
        try {
            // 生成水印编码
            String watermarkCode = encodeWatermarkInfo(watermarkInfo);
            String invisibleWatermark = encodeToZeroWidthChars(watermarkCode);

            // 在第一个文本节点中嵌入水印
            return xmlString.replaceFirst(">([^<]+)<", ">$1" + invisibleWatermark + "<");
            
        } catch (Exception e) {
            log.warn("XML水印注入失败: {}", e.getMessage());
            return xmlString;
        }
    }

    /**
     * 向文本中注入不可见水印
     */
    private String injectInvisibleWatermarkToText(String originalText, WatermarkInfo watermarkInfo) {
        try {
            // 生成水印编码
            String watermarkCode = encodeWatermarkInfo(watermarkInfo);
            String invisibleWatermark = encodeToZeroWidthChars(watermarkCode);

            // 智能嵌入位置选择
            List<Integer> embedPositions = selectEmbedPositions(originalText, invisibleWatermark.length());

            StringBuilder result = new StringBuilder(originalText);
            int offset = 0;

            for (int i = 0; i < invisibleWatermark.length() && i < embedPositions.size(); i++) {
                int position = embedPositions.get(i) + offset;
                char watermarkChar = invisibleWatermark.charAt(i);
                result.insert(position, watermarkChar);
                offset++;
            }

            return result.toString();

        } catch (Exception e) {
            log.warn("嵌入暗水印失败: {}", e.getMessage());
            return originalText;
        }
    }

    /**
     * 编码水印信息
     */
    private String encodeWatermarkInfo(WatermarkInfo watermarkInfo) {
        // 简化编码：IP_时间戳
        return watermarkInfo.getUserIp() + "_" + watermarkInfo.getTimestamp();
    }

    /**
     * 将文本编码为零宽字符
     */
    private String encodeToZeroWidthChars(String text) {
        StringBuilder result = new StringBuilder();
        
        for (char c : text.toCharArray()) {
            // 将字符转换为8位二进制
            String binary = String.format("%8s", Integer.toBinaryString(c)).replace(' ', '0');
            
            // 每2位二进制对应一个零宽字符
            for (int i = 0; i < binary.length(); i += 2) {
                String bits = binary.substring(i, Math.min(i + 2, binary.length()));
                Character zeroWidthChar = ENCODING_MAP.get(bits);
                if (zeroWidthChar != null) {
                    result.append(zeroWidthChar);
                }
            }
        }
        
        // 添加分隔符
        result.append(INVISIBLE_SEPARATOR);
        
        return result.toString();
    }

    /**
     * 从零宽字符解码文本
     */
    private String decodeFromZeroWidthChars(String zeroWidthText) {
        try {
            StringBuilder binaryBuilder = new StringBuilder();
            
            for (char c : zeroWidthText.toCharArray()) {
                if (c == INVISIBLE_SEPARATOR) {
                    break; // 遇到分隔符停止
                }
                
                String bits = DECODING_MAP.get(c);
                if (bits != null) {
                    binaryBuilder.append(bits);
                }
            }
            
            String binary = binaryBuilder.toString();
            StringBuilder result = new StringBuilder();
            
            // 每8位二进制转换为一个字符
            for (int i = 0; i < binary.length(); i += 8) {
                if (i + 8 <= binary.length()) {
                    String charBinary = binary.substring(i, i + 8);
                    int charCode = Integer.parseInt(charBinary, 2);
                    result.append((char) charCode);
                }
            }
            
            return result.toString();
        } catch (Exception e) {
            log.debug("零宽字符解码失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 提取零宽字符序列
     */
    private List<String> extractZeroWidthSequences(String content) {
        List<String> sequences = new ArrayList<>();
        StringBuilder currentSequence = new StringBuilder();
        
        for (char c : content.toCharArray()) {
            if (isZeroWidthChar(c)) {
                currentSequence.append(c);
            } else {
                if (currentSequence.length() > 0) {
                    sequences.add(currentSequence.toString());
                    currentSequence = new StringBuilder();
                }
            }
        }
        
        // 添加最后一个序列
        if (currentSequence.length() > 0) {
            sequences.add(currentSequence.toString());
        }
        
        return sequences;
    }

    /**
     * 检查是否为零宽字符
     */
    private boolean isZeroWidthChar(char c) {
        return c == ZERO_WIDTH_SPACE ||
               c == ZERO_WIDTH_NON_JOINER ||
               c == ZERO_WIDTH_JOINER ||
               c == WORD_JOINER ||
               c == INVISIBLE_SEPARATOR;
    }

    /**
     * 选择嵌入位置
     */
    private List<Integer> selectEmbedPositions(String text, int count) {
        List<Integer> positions = new ArrayList<>();
        
        if (text.length() <= count) {
            // 如果文本太短，均匀分布
            for (int i = 0; i < text.length() && positions.size() < count; i++) {
                positions.add(i);
            }
        } else {
            // 在空格和标点符号后嵌入
            for (int i = 0; i < text.length() && positions.size() < count; i++) {
                char c = text.charAt(i);
                if (c == ' ' || c == ',' || c == '.' || c == ';' || c == ':') {
                    positions.add(i + 1);
                }
            }
            
            // 如果位置不够，补充随机位置
            Random random = new Random();
            while (positions.size() < count) {
                int pos = random.nextInt(text.length());
                if (!positions.contains(pos)) {
                    positions.add(pos);
                }
            }
        }
        
        Collections.sort(positions);
        return positions;
    }

    /**
     * 解析水印信息
     */
    private WatermarkInfo parseWatermarkInfo(String decoded) {
        try {
            String[] parts = decoded.split("_");
            if (parts.length >= 2) {
                WatermarkInfo info = new WatermarkInfo();
                info.setUserIp(parts[0]);
                info.setTimestamp(Long.parseLong(parts[1]));
                info.setWatermarkText(decoded);
                return info;
            }
        } catch (Exception e) {
            log.debug("解析水印信息失败: {}", e.getMessage());
        }
        return null;
    }
}
